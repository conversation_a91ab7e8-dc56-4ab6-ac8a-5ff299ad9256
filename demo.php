<?php
/**
 * 演示页面 - 展示访问统计功能的使用
 */

// 设置页面标题
$page_title = '访问统计演示页面';

// 引入访问统计
require_once __DIR__ . '/track.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f5f7fa; }
        .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 12px; margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 15px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        
        .section { background: white; padding: 30px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .section h2 { color: #2c3e50; font-size: 1.8em; margin-bottom: 20px; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        .section h3 { color: #34495e; font-size: 1.3em; margin: 25px 0 15px 0; }
        
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0; }
        .feature-card { background: #f8f9fa; padding: 25px; border-radius: 8px; border-left: 4px solid #3498db; transition: transform 0.2s; }
        .feature-card:hover { transform: translateY(-2px); }
        .feature-card h4 { color: #2c3e50; margin-bottom: 10px; font-size: 1.2em; }
        .feature-card p { color: #7f8c8d; }
        
        .code-block { background: #2c3e50; color: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; overflow-x: auto; }
        .code-block code { font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 14px; }
        
        .stats-preview { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; margin: 30px 0; text-align: center; }
        .stats-preview h3 { margin-bottom: 20px; font-size: 1.5em; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; }
        .stat-item { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; transition: transform 0.2s; }
        .stat-item:hover { transform: scale(1.05); }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        
        .btn { display: inline-block; background: linear-gradient(135deg, #3498db, #2ecc71); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s; margin: 10px; }
        .btn:hover { transform: translateY(-2px); }
        .btn-secondary { background: linear-gradient(135deg, #95a5a6, #7f8c8d); }
        
        .info-box { background: #e8f4fd; border: 1px solid #bee5eb; color: #0c5460; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .info-box strong { color: #0c5460; }
        
        .current-visit { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .current-visit h4 { margin-bottom: 15px; color: #155724; }
        .current-visit ul { list-style: none; }
        .current-visit li { margin: 8px 0; }
        .current-visit strong { color: #155724; }
        
        .requirements { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .requirements ul { margin-left: 20px; }
        .requirements li { margin: 5px 0; }
        
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .header { padding: 25px; }
            .header h1 { font-size: 2em; }
            .section { padding: 20px; }
            .feature-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🚀 网站访问统计系统</h1>
            <p>基于PHP和MySQL的专业访问流量统计解决方案</p>
        </div>

        <!-- 系统介绍 -->
        <div class="section">
            <h2>📊 系统简介</h2>
            <p>这是一个功能完整的网站访问流量统计系统，使用PHP开发，数据存储在MySQL数据库中。系统能够实时统计网站访问量，提供详细的访问分析和可视化报表。</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🎯 精准统计</h4>
                    <p>实时记录访问量、独立访客、页面浏览量等关键指标，自动过滤爬虫和无效访问。</p>
                </div>
                <div class="feature-card">
                    <h4>📈 趋势分析</h4>
                    <p>提供访问趋势图表，支持多维度数据分析，帮助了解网站访问规律。</p>
                </div>
                <div class="feature-card">
                    <h4>🔍 详细报表</h4>
                    <p>热门页面排行、浏览器统计、设备分析等详细报表，全面了解访客行为。</p>
                </div>
                <div class="feature-card">
                    <h4>⚡ 高性能</h4>
                    <p>优化的数据库设计，支持高并发访问，不影响网站正常运行。</p>
                </div>
                <div class="feature-card">
                    <h4>🤖 智能过滤</h4>
                    <p>自动识别和过滤搜索引擎爬虫、恶意访问等无效流量。</p>
                </div>
                <div class="feature-card">
                    <h4>📱 多设备支持</h4>
                    <p>统计桌面、移动、平板等不同设备的访问情况，响应式设计。</p>
                </div>
            </div>
        </div>

        <!-- 使用方法 -->
        <div class="section">
            <h2>🛠️ 使用方法</h2>
            
            <h3>1. 基本集成</h3>
            <p>在需要统计访问量的页面中添加以下代码：</p>
            <div class="code-block">
                <code>
&lt;?php<br>
// 设置页面标题（可选）<br>
$page_title = '您的页面标题';<br>
<br>
// 引入访问统计<br>
require_once 'track.php';<br>
?&gt;
                </code>
            </div>

            <h3>2. 查看统计数据</h3>
            <p>访问统计仪表板查看详细的访问数据：</p>
            <div class="code-block">
                <code>
// 访问以下URL查看统计数据<br>
http://yoursite.com/dashboard.php
                </code>
            </div>

            <h3>3. 自定义配置</h3>
            <p>修改数据库配置文件以适应您的环境：</p>
            <div class="code-block">
                <code>
// config/database.php<br>
return [<br>
&nbsp;&nbsp;&nbsp;&nbsp;'host' => 'localhost',<br>
&nbsp;&nbsp;&nbsp;&nbsp;'database' => 'your_database',<br>
&nbsp;&nbsp;&nbsp;&nbsp;'username' => 'your_username',<br>
&nbsp;&nbsp;&nbsp;&nbsp;'password' => 'your_password'<br>
];
                </code>
            </div>
        </div>

        <!-- 当前访问信息 -->
        <div class="current-visit">
            <h4>✅ 您的访问已被记录</h4>
            <ul>
                <li><strong>访问时间：</strong><?php echo date('Y-m-d H:i:s'); ?></li>
                <li><strong>页面URL：</strong><?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?></li>
                <li><strong>IP地址：</strong><?php echo $_SERVER['REMOTE_ADDR'] ? $_SERVER['REMOTE_ADDR'] : '未知'; ?></li>
                <li><strong>用户代理：</strong><?php echo htmlspecialchars(substr($_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '未知', 0, 80)); ?>...</li>
                <li><strong>来源页面：</strong><?php echo htmlspecialchars($_SERVER['HTTP_REFERER'] ? $_SERVER['HTTP_REFERER'] : '直接访问'); ?></li>
            </ul>
        </div>

        <!-- 统计预览 -->
        <div class="stats-preview">
            <h3>📊 实时统计预览</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="todayVisits">-</div>
                    <div class="stat-label">今日访问量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="todayVisitors">-</div>
                    <div class="stat-label">今日访客</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalPages">-</div>
                    <div class="stat-label">页面浏览量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="onlineUsers">-</div>
                    <div class="stat-label">在线用户</div>
                </div>
            </div>
        </div>

        <!-- 系统要求 -->
        <div class="section">
            <h2>⚙️ 系统要求</h2>
            <div class="requirements">
                <ul>
                    <li><strong>PHP版本：</strong> 7.0 或更高版本</li>
                    <li><strong>数据库：</strong> MySQL 5.7 或更高版本</li>
                    <li><strong>PHP扩展：</strong> PDO, PDO_MySQL</li>
                    <li><strong>权限：</strong> 文件读写权限，数据库操作权限</li>
                    <li><strong>存储空间：</strong> 建议至少100MB可用空间</li>
                </ul>
            </div>
        </div>

        <!-- 功能特性 -->
        <div class="section">
            <h2>✨ 功能特性</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📅 按天统计</h4>
                    <p>自动按日期统计访问数据，支持历史数据查询和趋势分析。</p>
                </div>
                <div class="feature-card">
                    <h4>👥 访客识别</h4>
                    <p>基于Cookie和IP的访客识别，准确统计独立访客数量。</p>
                </div>
                <div class="feature-card">
                    <h4>🔥 热门页面</h4>
                    <p>统计最受欢迎的页面，了解用户最关注的内容。</p>
                </div>
                <div class="feature-card">
                    <h4>🌐 来源分析</h4>
                    <p>分析访问来源，包括搜索引擎、社交媒体、直接访问等。</p>
                </div>
                <div class="feature-card">
                    <h4>⏰ 实时监控</h4>
                    <p>实时记录访问数据，支持在线用户数和最近访问记录查看。</p>
                </div>
                <div class="feature-card">
                    <h4>📊 数据导出</h4>
                    <p>支持统计数据导出，便于进一步分析和报告。</p>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div style="text-align: center; margin: 40px 0;">
            <a href="dashboard.php" class="btn">📊 查看统计仪表板</a>
            <a href="install.php" class="btn btn-secondary">⚙️ 系统安装</a>
        </div>

        <!-- 信息提示 -->
        <div class="info-box">
            <strong>💡 提示：</strong>
            本页面已经集成了访问统计功能，您的访问数据已被记录到数据库中。
            您可以通过访问统计仪表板查看详细的访问数据和分析报表。
        </div>

        <!-- 页面底部 -->
        <div style="text-align: center; margin-top: 50px; padding: 30px; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">🎯 开始使用</h3>
            <p style="color: #7f8c8d; margin-bottom: 20px;">
                立即在您的网站中集成这个强大的访问统计系统，获得详细的访问分析数据！
            </p>
            <a href="install.php" class="btn">🚀 立即安装</a>
        </div>
    </div>

    <script>
        // 模拟实时数据更新（实际项目中可以通过AJAX获取真实数据）
        function updateStats() {
            document.getElementById('todayVisits').textContent = Math.floor(Math.random() * 1000) + 100;
            document.getElementById('todayVisitors').textContent = Math.floor(Math.random() * 500) + 50;
            document.getElementById('totalPages').textContent = Math.floor(Math.random() * 2000) + 200;
            document.getElementById('onlineUsers').textContent = Math.floor(Math.random() * 50) + 5;
        }
        
        // 页面加载时更新一次数据
        updateStats();
        
        // 每30秒更新一次数据
        setInterval(updateStats, 30000);
    </script>
</body>
</html>
