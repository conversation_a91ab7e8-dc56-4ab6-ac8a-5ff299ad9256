<?php
/**
 * PHPCMS访问统计系统安装脚本
 * 
 * 使用方法：
 * 1. 配置 config/database.php 中的数据库连接信息
 * 2. 在浏览器中访问此文件进行安装
 */

// 防止直接访问
if (!defined('IN_PHPCMS')) {
    define('IN_PHPCMS', true);
}

require_once __DIR__ . '/config/database.php';

// 检查是否已经安装
$lockFile = __DIR__ . '/install.lock';
if (file_exists($lockFile) && !isset($_GET['force'])) {
    die('系统已经安装！如需重新安装，请删除 install.lock 文件或在URL后添加 ?force=1');
}

$message = '';
$error = '';

// 处理安装请求
if ($_POST['action'] === 'install') {
    try {
        $result = installDatabase();
        if ($result['success']) {
            // 创建锁定文件
            file_put_contents($lockFile, date('Y-m-d H:i:s'));
            $message = $result['message'];
        } else {
            $error = $result['message'];
        }
    } catch (Exception $e) {
        $error = '安装失败：' . $e->getMessage();
    }
}

/**
 * 安装数据库
 */
function installDatabase() {
    try {
        // 获取数据库配置
        $config = get_db_config('default');
        
        // 连接数据库
        $dsn = build_dsn($config);
        $pdo = new PDO($dsn, $config['username'], $config['password'], get_pdo_options());
        
        // 读取SQL文件
        $sqlFile = __DIR__ . '/sql/visit_stats_tables.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('SQL文件不存在：' . $sqlFile);
        }
        
        $sql = file_get_contents($sqlFile);
        
        // 替换表前缀
        $sql = str_replace('phpcms_', $config['prefix'], $sql);
        
        // 执行SQL语句
        $statements = explode(';', $sql);
        $executedCount = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(SET|DROP|--)/i', $statement)) {
                $pdo->exec($statement);
                $executedCount++;
            }
        }
        
        // 插入初始数据
        insertInitialData($pdo, $config['prefix']);
        
        return array(
            'success' => true,
            'message' => "安装成功！共执行了 {$executedCount} 个SQL语句。<br>" .
                        "数据库表已创建完成，您现在可以开始使用访问统计功能。<br>" .
                        "<a href='admin/visit_stats.php'>点击这里查看统计数据</a>"
        );
        
    } catch (PDOException $e) {
        return array(
            'success' => false,
            'message' => '数据库错误：' . $e->getMessage()
        );
    } catch (Exception $e) {
        return array(
            'success' => false,
            'message' => $e->getMessage()
        );
    }
}

/**
 * 插入初始数据
 */
function insertInitialData($pdo, $prefix) {
    // 插入示例统计数据（可选）
    $today = date('Y-m-d');
    $yesterday = date('Y-m-d', strtotime('-1 day'));
    
    // 插入网站总体统计
    $sql = "INSERT INTO `{$prefix}site_stats` 
            (stat_date, total_visits, unique_visitors, page_views, new_visitors, returning_visitors) 
            VALUES 
            (?, 0, 0, 0, 0, 0),
            (?, 0, 0, 0, 0, 0)
            ON DUPLICATE KEY UPDATE stat_date = stat_date";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute(array($yesterday, $today));
}

/**
 * 检查系统要求
 */
function checkRequirements() {
    $requirements = array();
    
    // PHP版本检查
    $requirements['php_version'] = array(
        'name' => 'PHP版本 (>= 7.0)',
        'status' => version_compare(PHP_VERSION, '7.0.0', '>='),
        'current' => PHP_VERSION
    );
    
    // PDO扩展检查
    $requirements['pdo'] = array(
        'name' => 'PDO扩展',
        'status' => extension_loaded('pdo'),
        'current' => extension_loaded('pdo') ? '已安装' : '未安装'
    );
    
    // PDO MySQL驱动检查
    $requirements['pdo_mysql'] = array(
        'name' => 'PDO MySQL驱动',
        'status' => extension_loaded('pdo_mysql'),
        'current' => extension_loaded('pdo_mysql') ? '已安装' : '未安装'
    );
    
    // 目录写入权限检查
    $requirements['logs_writable'] = array(
        'name' => 'logs目录写入权限',
        'status' => is_writable(__DIR__ . '/logs') || is_writable(__DIR__),
        'current' => is_writable(__DIR__ . '/logs') ? '可写' : (is_writable(__DIR__) ? '可写' : '不可写')
    );
    
    return $requirements;
}

/**
 * 测试数据库连接
 */
function testDatabaseConnection() {
    try {
        $config = get_db_config('default');
        $dsn = build_dsn($config);
        $pdo = new PDO($dsn, $config['username'], $config['password'], get_pdo_options());
        return array('success' => true, 'message' => '数据库连接成功');
    } catch (PDOException $e) {
        return array('success' => false, 'message' => '数据库连接失败：' . $e->getMessage());
    }
}

$requirements = checkRequirements();
$dbTest = testDatabaseConnection();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHPCMS访问统计系统 - 安装向导</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #007cba; padding-bottom: 20px; margin-bottom: 30px; }
        .header h1 { color: #007cba; margin: 0; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        .requirement { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; }
        .requirement:last-child { border-bottom: none; }
        .status { padding: 5px 10px; border-radius: 4px; color: white; font-weight: bold; }
        .status.success { background-color: #28a745; }
        .status.error { background-color: #dc3545; }
        .message { padding: 15px; border-radius: 4px; margin: 15px 0; }
        .message.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .install-btn { background: #007cba; color: white; padding: 15px 30px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; width: 100%; }
        .install-btn:hover { background: #005a87; }
        .install-btn:disabled { background: #ccc; cursor: not-allowed; }
        .config-info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .config-info code { background: #e9ecef; padding: 2px 4px; border-radius: 2px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PHPCMS访问统计系统</h1>
            <p>安装向导</p>
        </div>

        <?php if ($message): ?>
            <div class="message success"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="message error"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- 系统要求检查 -->
        <div class="section">
            <h2>系统要求检查</h2>
            <?php foreach ($requirements as $req): ?>
                <div class="requirement">
                    <span><?php echo $req['name']; ?></span>
                    <div>
                        <span><?php echo $req['current']; ?></span>
                        <span class="status <?php echo $req['status'] ? 'success' : 'error'; ?>">
                            <?php echo $req['status'] ? '通过' : '失败'; ?>
                        </span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- 数据库连接测试 -->
        <div class="section">
            <h2>数据库连接测试</h2>
            <div class="requirement">
                <span>数据库连接</span>
                <div>
                    <span><?php echo $dbTest['message']; ?></span>
                    <span class="status <?php echo $dbTest['success'] ? 'success' : 'error'; ?>">
                        <?php echo $dbTest['success'] ? '成功' : '失败'; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- 配置信息 -->
        <div class="section">
            <h2>配置信息</h2>
            <div class="config-info">
                <p><strong>数据库配置文件：</strong> <code>config/database.php</code></p>
                <p><strong>管理页面：</strong> <code>admin/visit_stats.php</code></p>
                <p><strong>默认管理密码：</strong> <code>admin123</code> (安装后请及时修改)</p>
                <p><strong>使用方法：</strong> 在需要统计的页面中添加 <code>require_once 'track_visit.php';</code></p>
            </div>
        </div>

        <!-- 安装按钮 -->
        <div class="section">
            <?php 
            $canInstall = true;
            foreach ($requirements as $req) {
                if (!$req['status']) {
                    $canInstall = false;
                    break;
                }
            }
            $canInstall = $canInstall && $dbTest['success'];
            ?>
            
            <form method="POST">
                <input type="hidden" name="action" value="install">
                <button type="submit" class="install-btn" <?php echo $canInstall ? '' : 'disabled'; ?>>
                    <?php echo $canInstall ? '开始安装' : '请先解决上述问题'; ?>
                </button>
            </form>
        </div>
    </div>
</body>
</html>
