<?php
/**
 * 网站访问统计系统安装脚本
 */

// 检查是否已经安装
$lockFile = __DIR__ . '/install.lock';
if (file_exists($lockFile) && !isset($_GET['force'])) {
    die('
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 100px auto; padding: 40px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; text-align: center;">
        <h2 style="color: #721c24;">系统已经安装！</h2>
        <p style="color: #721c24;">如需重新安装，请删除 install.lock 文件或在URL后添加 ?force=1</p>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">查看统计仪表板</a>
    </div>
    ');
}

$message = '';
$error = '';
$step = $_GET['step'] ?? 1;

// 处理安装步骤
if ($_POST['action'] === 'install') {
    try {
        $result = installDatabase($_POST);
        if ($result['success']) {
            // 创建锁定文件
            file_put_contents($lockFile, date('Y-m-d H:i:s'));
            $message = $result['message'];
            $step = 'complete';
        } else {
            $error = $result['message'];
        }
    } catch (Exception $e) {
        $error = '安装失败：' . $e->getMessage();
    }
}

/**
 * 安装数据库
 */
function installDatabase($config) {
    try {
        // 验证配置
        if (empty($config['host']) || empty($config['database']) || empty($config['username'])) {
            throw new Exception('请填写完整的数据库配置信息');
        }
        
        // 连接数据库
        $dsn = sprintf(
            'mysql:host=%s;port=%d;charset=utf8mb4',
            $config['host'],
            $config['port'] ?: 3306
        );
        
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // 创建数据库（如果不存在）
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$config['database']}`");
        
        // 更新数据库配置文件
        updateDatabaseConfig($config);
        
        // 执行SQL文件
        $sqlFile = __DIR__ . '/database/visit_stats.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('SQL文件不存在：' . $sqlFile);
        }
        
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        $executedCount = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(SET|DROP|--)/i', $statement)) {
                $pdo->exec($statement);
                $executedCount++;
            }
        }
        
        return [
            'success' => true,
            'message' => "安装成功！共执行了 {$executedCount} 个SQL语句。<br>" .
                        "数据库表已创建完成，您现在可以开始使用访问统计功能。"
        ];
        
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => '数据库错误：' . $e->getMessage()
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * 更新数据库配置文件
 */
function updateDatabaseConfig($config) {
    $configContent = "<?php\n/**\n * 数据库配置文件\n */\n\nreturn [\n";
    $configContent .= "    'host' => '{$config['host']}',\n";
    $configContent .= "    'port' => {$config['port']},\n";
    $configContent .= "    'database' => '{$config['database']}',\n";
    $configContent .= "    'username' => '{$config['username']}',\n";
    $configContent .= "    'password' => '{$config['password']}',\n";
    $configContent .= "    'charset' => 'utf8mb4',\n";
    $configContent .= "    'options' => [\n";
    $configContent .= "        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n";
    $configContent .= "        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,\n";
    $configContent .= "        PDO::ATTR_EMULATE_PREPARES => false,\n";
    $configContent .= "        PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci\"\n";
    $configContent .= "    ]\n";
    $configContent .= "];\n";
    
    $configDir = __DIR__ . '/config';
    if (!is_dir($configDir)) {
        mkdir($configDir, 0755, true);
    }
    
    file_put_contents($configDir . '/database.php', $configContent);
}

/**
 * 检查系统要求
 */
function checkRequirements() {
    $requirements = [];
    
    // PHP版本检查
    $requirements['php_version'] = [
        'name' => 'PHP版本 (>= 7.0)',
        'status' => version_compare(PHP_VERSION, '7.0.0', '>='),
        'current' => PHP_VERSION
    ];
    
    // PDO扩展检查
    $requirements['pdo'] = [
        'name' => 'PDO扩展',
        'status' => extension_loaded('pdo'),
        'current' => extension_loaded('pdo') ? '已安装' : '未安装'
    ];
    
    // PDO MySQL驱动检查
    $requirements['pdo_mysql'] = [
        'name' => 'PDO MySQL驱动',
        'status' => extension_loaded('pdo_mysql'),
        'current' => extension_loaded('pdo_mysql') ? '已安装' : '未安装'
    ];
    
    // 目录写入权限检查
    $requirements['writable'] = [
        'name' => '目录写入权限',
        'status' => is_writable(__DIR__),
        'current' => is_writable(__DIR__) ? '可写' : '不可写'
    ];
    
    return $requirements;
}

$requirements = checkRequirements();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站访问统计系统 - 安装向导</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .install-card { background: white; border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }
        .header { background: linear-gradient(135deg, #3498db, #2ecc71); color: white; padding: 40px; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .content { padding: 40px; }
        
        .step-indicator { display: flex; justify-content: center; margin-bottom: 40px; }
        .step { width: 40px; height: 40px; border-radius: 50%; background: #ecf0f1; color: #7f8c8d; display: flex; align-items: center; justify-content: center; font-weight: bold; margin: 0 10px; position: relative; }
        .step.active { background: #3498db; color: white; }
        .step.complete { background: #2ecc71; color: white; }
        .step::after { content: ''; position: absolute; top: 50%; left: 100%; width: 40px; height: 2px; background: #ecf0f1; z-index: -1; }
        .step:last-child::after { display: none; }
        
        .section { margin-bottom: 30px; }
        .section h2 { color: #2c3e50; margin-bottom: 20px; font-size: 1.5em; }
        
        .requirement { display: flex; justify-content: space-between; align-items: center; padding: 15px; border-bottom: 1px solid #ecf0f1; }
        .requirement:last-child { border-bottom: none; }
        .status { padding: 5px 15px; border-radius: 20px; color: white; font-weight: bold; font-size: 0.9em; }
        .status.success { background: #2ecc71; }
        .status.error { background: #e74c3c; }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; color: #2c3e50; font-weight: 600; }
        .form-group input { width: 100%; padding: 12px; border: 2px solid #ecf0f1; border-radius: 6px; font-size: 16px; transition: border-color 0.3s; }
        .form-group input:focus { outline: none; border-color: #3498db; }
        
        .btn { background: linear-gradient(135deg, #3498db, #2ecc71); color: white; border: none; padding: 15px 30px; border-radius: 6px; font-size: 16px; font-weight: bold; cursor: pointer; transition: transform 0.2s; text-decoration: none; display: inline-block; }
        .btn:hover { transform: translateY(-2px); }
        .btn:disabled { background: #bdc3c7; cursor: not-allowed; transform: none; }
        .btn-test { background: linear-gradient(135deg, #f39c12, #e67e22); }
        
        .message { padding: 20px; border-radius: 6px; margin: 20px 0; }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        .complete-icon { font-size: 4em; margin-bottom: 20px; }
        .feature-list { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .feature-list ul { list-style: none; }
        .feature-list li { margin: 8px 0; padding-left: 20px; position: relative; }
        .feature-list li::before { content: '✓'; position: absolute; left: 0; color: #2ecc71; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-card">
            <div class="header">
                <h1>🚀 安装向导</h1>
                <p>网站访问统计系统</p>
            </div>
            
            <div class="content">
                <?php if ($step != 'complete'): ?>
                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? 'active' : ''; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? 'active' : ''; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? 'active' : ''; ?>">3</div>
                </div>
                <?php endif; ?>

                <?php if ($message): ?>
                    <div class="message success"><?php echo $message; ?></div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="message error"><?php echo $error; ?></div>
                <?php endif; ?>

                <?php if ($step == 1): ?>
                <!-- 步骤1：系统要求检查 -->
                <div class="section">
                    <h2>📋 系统要求检查</h2>
                    <?php foreach ($requirements as $req): ?>
                        <div class="requirement">
                            <div>
                                <strong><?php echo $req['name']; ?></strong>
                                <br><small style="color: #7f8c8d;"><?php echo $req['current']; ?></small>
                            </div>
                            <span class="status <?php echo $req['status'] ? 'success' : 'error'; ?>">
                                <?php echo $req['status'] ? '✓ 通过' : '✗ 失败'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php 
                $canProceed = true;
                foreach ($requirements as $req) {
                    if (!$req['status']) {
                        $canProceed = false;
                        break;
                    }
                }
                ?>

                <div style="text-align: center;">
                    <?php if ($canProceed): ?>
                        <a href="?step=2" class="btn">下一步：数据库配置</a>
                    <?php else: ?>
                        <button class="btn" disabled>请先解决上述问题</button>
                    <?php endif; ?>
                </div>

                <?php elseif ($step == 2): ?>
                <!-- 步骤2：数据库配置 -->
                <div class="section">
                    <h2>🗄️ 数据库配置</h2>
                    <form method="POST" id="dbForm">
                        <input type="hidden" name="action" value="install">
                        
                        <div class="form-group">
                            <label for="host">数据库主机</label>
                            <input type="text" id="host" name="host" value="localhost" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="port">端口</label>
                            <input type="number" id="port" name="port" value="3306" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="database">数据库名</label>
                            <input type="text" id="database" name="database" value="visit_stats" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="username">用户名</label>
                            <input type="text" id="username" name="username" value="root" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">密码</label>
                            <input type="password" id="password" name="password">
                        </div>
                        
                        <div style="text-align: center;">
                            <button type="submit" class="btn">开始安装</button>
                        </div>
                    </form>
                </div>

                <?php elseif ($step == 'complete'): ?>
                <!-- 安装完成 -->
                <div style="text-align: center;">
                    <div class="complete-icon">🎉</div>
                    <h2 style="color: #2ecc71; margin-bottom: 20px;">安装完成！</h2>
                    <p style="color: #7f8c8d; margin-bottom: 30px;">
                        网站访问统计系统已成功安装，您现在可以开始使用了。
                    </p>
                    
                    <div class="feature-list">
                        <h3 style="color: #2c3e50; margin-bottom: 15px;">📝 使用说明</h3>
                        <ul>
                            <li>在需要统计的页面中添加：<code style="background: #e9ecef; padding: 2px 6px; border-radius: 3px;">require_once 'track.php';</code></li>
                            <li>访问统计仪表板查看数据：<a href="dashboard.php" style="color: #3498db;">dashboard.php</a></li>
                            <li>查看演示页面：<a href="demo.php" style="color: #3498db;">demo.php</a></li>
                            <li>系统会自动记录访问数据并生成统计报表</li>
                        </ul>
                    </div>
                    
                    <a href="dashboard.php" class="btn">🚀 查看统计仪表板</a>
                    <a href="demo.php" class="btn btn-test">📖 查看演示</a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
