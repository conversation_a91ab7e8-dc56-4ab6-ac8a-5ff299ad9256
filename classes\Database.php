<?php
/**
 * 数据库操作类
 * 使用单例模式管理数据库连接
 */

class Database {
    private static $instance = null;
    private $pdo;
    private $config;
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private function __construct() {
        $this->config = require __DIR__ . '/../config/database.php';
        $this->connect();
    }
    
    /**
     * 获取数据库实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 建立数据库连接
     */
    private function connect() {
        try {
            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                $this->config['host'],
                $this->config['port'],
                $this->config['database'],
                $this->config['charset']
            );
            
            $this->pdo = new PDO($dsn, $this->config['username'], $this->config['password'], $this->config['options']);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取PDO实例
     */
    public function getPdo() {
        return $this->pdo;
    }
    
    /**
     * 执行查询
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception('查询执行失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取单行数据
     */
    public function fetchRow($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 获取多行数据
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 插入数据
     */
    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = ':' . implode(', :', $fields);
        $sql = "INSERT INTO {$table} (" . implode(', ', $fields) . ") VALUES ({$placeholders})";
        
        $stmt = $this->query($sql, $data);
        return $this->pdo->lastInsertId();
    }
    
    /**
     * 更新数据
     */
    public function update($table, $data, $where, $whereParams = []) {
        $fields = [];
        foreach (array_keys($data) as $field) {
            $fields[] = "{$field} = :{$field}";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $fields) . " WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 删除数据
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    /**
     * 获取配置值
     */
    public function getConfig($key, $default = null) {
        $sql = "SELECT config_value FROM visit_config WHERE config_key = ?";
        $result = $this->fetchRow($sql, [$key]);
        return $result ? $result['config_value'] : $default;
    }
    
    /**
     * 设置配置值
     */
    public function setConfig($key, $value, $description = '') {
        $sql = "INSERT INTO visit_config (config_key, config_value, description) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), description = VALUES(description)";
        return $this->query($sql, [$key, $value, $description]);
    }
    
    /**
     * 执行SQL文件
     */
    public function executeSqlFile($filePath) {
        if (!file_exists($filePath)) {
            throw new Exception("SQL文件不存在: {$filePath}");
        }
        
        $sql = file_get_contents($filePath);
        $statements = explode(';', $sql);
        $executedCount = 0;
        
        $this->beginTransaction();
        
        try {
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^(SET|DROP|--)/i', $statement)) {
                    $this->pdo->exec($statement);
                    $executedCount++;
                }
            }
            
            $this->commit();
            return $executedCount;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * 检查表是否存在
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->fetchRow($sql, [$tableName]);
        return !empty($result);
    }
    
    /**
     * 获取表记录数
     */
    public function getTableCount($tableName) {
        $sql = "SELECT COUNT(*) as count FROM {$tableName}";
        $result = $this->fetchRow($sql);
        return $result['count'];
    }
    
    /**
     * 防止克隆
     */
    private function __clone() {}
    
    /**
     * 防止反序列化
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}
