<?php
/**
 * 简单的网站访问量统计功能
 * 使用文件存储访问数据，无需数据库
 */

// 配置
$data_file = __DIR__ . '/visit_data.txt';  // 存储访问数据的文件
$log_file = __DIR__ . '/visit_log.txt';    // 访问日志文件

/**
 * 记录访问
 */
function recordVisit($data_file, $log_file) {
    $today = date('Y-m-d');
    $current_time = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $page_url = $_SERVER['REQUEST_URI'] ?? '/';
    
    // 检查是否为有效访问（排除爬虫和重复访问）
    if (!isValidVisit($ip, $user_agent)) {
        return false;
    }
    
    // 读取现有数据
    $data = loadVisitData($data_file);
    
    // 更新今日访问量
    if (!isset($data[$today])) {
        $data[$today] = [
            'total_visits' => 0,
            'unique_visitors' => [],
            'page_views' => []
        ];
    }
    
    // 增加总访问量
    $data[$today]['total_visits']++;
    
    // 记录独立访客（基于IP）
    if (!in_array($ip, $data[$today]['unique_visitors'])) {
        $data[$today]['unique_visitors'][] = $ip;
    }
    
    // 记录页面访问
    if (!isset($data[$today]['page_views'][$page_url])) {
        $data[$today]['page_views'][$page_url] = 0;
    }
    $data[$today]['page_views'][$page_url]++;
    
    // 保存数据
    saveVisitData($data_file, $data);
    
    // 记录访问日志
    $log_entry = sprintf(
        "[%s] IP: %s | Page: %s | User-Agent: %s\n",
        $current_time,
        $ip,
        $page_url,
        substr($user_agent, 0, 100)
    );
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    
    return true;
}

/**
 * 检查是否为有效访问
 */
function isValidVisit($ip, $user_agent) {
    // 排除本地IP
    if (in_array($ip, ['127.0.0.1', '::1', 'localhost'])) {
        return false;
    }
    
    // 排除爬虫
    $bot_patterns = ['bot', 'crawler', 'spider', 'scraper', 'curl', 'wget'];
    $user_agent_lower = strtolower($user_agent);
    
    foreach ($bot_patterns as $pattern) {
        if (strpos($user_agent_lower, $pattern) !== false) {
            return false;
        }
    }
    
    return true;
}

/**
 * 加载访问数据
 */
function loadVisitData($data_file) {
    if (!file_exists($data_file)) {
        return [];
    }
    
    $content = file_get_contents($data_file);
    $data = json_decode($content, true);
    
    return $data ?: [];
}

/**
 * 保存访问数据
 */
function saveVisitData($data_file, $data) {
    // 只保留最近30天的数据
    $cutoff_date = date('Y-m-d', strtotime('-30 days'));
    foreach ($data as $date => $info) {
        if ($date < $cutoff_date) {
            unset($data[$date]);
        }
    }
    
    $json_data = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    file_put_contents($data_file, $json_data, LOCK_EX);
}

/**
 * 获取访问统计
 */
function getVisitStats($data_file, $days = 7) {
    $data = loadVisitData($data_file);
    $stats = [
        'today' => [
            'total_visits' => 0,
            'unique_visitors' => 0,
            'page_views' => 0
        ],
        'recent_days' => [],
        'total_recent' => [
            'total_visits' => 0,
            'unique_visitors' => 0,
            'page_views' => 0
        ],
        'popular_pages' => []
    ];
    
    $today = date('Y-m-d');
    $all_visitors = [];
    $all_pages = [];
    
    // 计算最近几天的统计
    for ($i = 0; $i < $days; $i++) {
        $date = date('Y-m-d', strtotime("-{$i} days"));
        
        if (isset($data[$date])) {
            $day_data = $data[$date];
            $unique_count = count($day_data['unique_visitors']);
            $page_views = array_sum($day_data['page_views']);
            
            $stats['recent_days'][$date] = [
                'total_visits' => $day_data['total_visits'],
                'unique_visitors' => $unique_count,
                'page_views' => $page_views
            ];
            
            // 累计统计
            $stats['total_recent']['total_visits'] += $day_data['total_visits'];
            $all_visitors = array_merge($all_visitors, $day_data['unique_visitors']);
            
            foreach ($day_data['page_views'] as $page => $views) {
                if (!isset($all_pages[$page])) {
                    $all_pages[$page] = 0;
                }
                $all_pages[$page] += $views;
            }
            
            // 今日统计
            if ($date === $today) {
                $stats['today'] = [
                    'total_visits' => $day_data['total_visits'],
                    'unique_visitors' => $unique_count,
                    'page_views' => $page_views
                ];
            }
        } else {
            $stats['recent_days'][$date] = [
                'total_visits' => 0,
                'unique_visitors' => 0,
                'page_views' => 0
            ];
        }
    }
    
    $stats['total_recent']['unique_visitors'] = count(array_unique($all_visitors));
    $stats['total_recent']['page_views'] = array_sum($all_pages);
    
    // 热门页面排序
    arsort($all_pages);
    $stats['popular_pages'] = array_slice($all_pages, 0, 10, true);
    
    return $stats;
}

/**
 * 显示访问统计
 */
function displayVisitStats($data_file) {
    $stats = getVisitStats($data_file);
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; font-family: Arial, sans-serif;'>";
    echo "<h3 style='color: #007cba; margin-top: 0;'>📊 网站访问统计</h3>";
    
    // 今日统计
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px;'>";
    echo "<div style='background: white; padding: 15px; border-radius: 6px; text-align: center; border-left: 4px solid #28a745;'>";
    echo "<div style='font-size: 24px; font-weight: bold; color: #28a745;'>{$stats['today']['total_visits']}</div>";
    echo "<div style='color: #666; font-size: 14px;'>今日访问量</div>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 6px; text-align: center; border-left: 4px solid #007cba;'>";
    echo "<div style='font-size: 24px; font-weight: bold; color: #007cba;'>{$stats['today']['unique_visitors']}</div>";
    echo "<div style='color: #666; font-size: 14px;'>今日独立访客</div>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 6px; text-align: center; border-left: 4px solid #ffc107;'>";
    echo "<div style='font-size: 24px; font-weight: bold; color: #ffc107;'>{$stats['today']['page_views']}</div>";
    echo "<div style='color: #666; font-size: 14px;'>今日页面浏览量</div>";
    echo "</div>";
    echo "</div>";
    
    // 最近7天统计
    echo "<h4 style='color: #333; margin-bottom: 10px;'>📈 最近7天访问趋势</h4>";
    echo "<div style='background: white; padding: 15px; border-radius: 6px; margin-bottom: 20px;'>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px; text-align: left; border-bottom: 1px solid #ddd;'>日期</th>";
    echo "<th style='padding: 8px; text-align: center; border-bottom: 1px solid #ddd;'>访问量</th>";
    echo "<th style='padding: 8px; text-align: center; border-bottom: 1px solid #ddd;'>独立访客</th>";
    echo "<th style='padding: 8px; text-align: center; border-bottom: 1px solid #ddd;'>页面浏览量</th>";
    echo "</tr>";
    
    foreach ($stats['recent_days'] as $date => $day_stats) {
        $is_today = ($date === date('Y-m-d'));
        $row_style = $is_today ? 'background: #e8f5e8;' : '';
        
        echo "<tr style='{$row_style}'>";
        echo "<td style='padding: 8px; border-bottom: 1px solid #eee;'>{$date}" . ($is_today ? ' (今天)' : '') . "</td>";
        echo "<td style='padding: 8px; text-align: center; border-bottom: 1px solid #eee;'>{$day_stats['total_visits']}</td>";
        echo "<td style='padding: 8px; text-align: center; border-bottom: 1px solid #eee;'>{$day_stats['unique_visitors']}</td>";
        echo "<td style='padding: 8px; text-align: center; border-bottom: 1px solid #eee;'>{$day_stats['page_views']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // 热门页面
    if (!empty($stats['popular_pages'])) {
        echo "<h4 style='color: #333; margin-bottom: 10px;'>🔥 热门页面</h4>";
        echo "<div style='background: white; padding: 15px; border-radius: 6px;'>";
        echo "<ol style='margin: 0; padding-left: 20px;'>";
        foreach ($stats['popular_pages'] as $page => $views) {
            echo "<li style='margin-bottom: 5px;'>";
            echo "<span style='color: #007cba; font-weight: bold;'>{$page}</span> ";
            echo "<span style='color: #666;'>({$views} 次访问)</span>";
            echo "</li>";
        }
        echo "</ol>";
        echo "</div>";
    }
    
    echo "</div>";
}

// 自动记录访问（如果直接访问此文件）
if (basename($_SERVER['PHP_SELF']) === 'simple_visit_counter.php') {
    recordVisit($data_file, $log_file);
}
?>
