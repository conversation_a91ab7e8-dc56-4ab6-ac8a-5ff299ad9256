<?php
/**
 * 访问统计入口文件
 * 在需要统计访问量的页面中引入此文件
 * 
 * 使用方法：
 * require_once 'track.php';
 * 
 * 或者带参数：
 * $page_title = '页面标题';
 * require_once 'track.php';
 */

// 防止重复包含
if (defined('VISIT_TRACKER_LOADED')) {
    return;
}
define('VISIT_TRACKER_LOADED', true);

// 引入核心类
require_once __DIR__ . '/classes/VisitTracker.php';

try {
    // 创建访问统计实例
    $visitTracker = new VisitTracker();
    
    // 获取当前页面信息
    $pageUrl = getCurrentPageUrl();
    $pageTitle = isset($page_title) ? $page_title : getPageTitle();
    $refererUrl = $_SERVER['HTTP_REFERER'] ? $_SERVER['HTTP_REFERER'] : null;
    
    // 记录访问
    $visitTracker->recordVisit($pageUrl, $pageTitle, $refererUrl);
    
} catch (Exception $e) {
    // 静默处理错误，不影响页面正常显示
    error_log("访问统计错误: " . $e->getMessage());
}

/**
 * 获取当前页面完整URL
 */
function getCurrentPageUrl() {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ? $_SERVER['HTTP_HOST'] : 'localhost';
    $uri = $_SERVER['REQUEST_URI'] ? $_SERVER['REQUEST_URI'] : '/';
    
    return $protocol . $host . $uri;
}

/**
 * 获取页面标题
 */
function getPageTitle() {
    // 尝试从HTML标题标签获取
    if (function_exists('get_page_title')) {
        return get_page_title();
    }
    
    // 从URL路径推断标题
    $path = parse_url($_SERVER['REQUEST_URI'] ? $_SERVER['REQUEST_URI'] : '/', PHP_URL_PATH);
    $segments = array_filter(explode('/', $path));
    
    if (empty($segments)) {
        return '首页';
    }
    
    return ucfirst(end($segments));
}
