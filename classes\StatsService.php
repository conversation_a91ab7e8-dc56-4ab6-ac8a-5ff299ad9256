<?php
/**
 * 统计数据服务类
 * 提供各种统计数据查询功能
 */

require_once __DIR__ . '/Database.php';

class StatsService {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 获取每日统计数据
     */
    public function getDailyStats($dateFrom = null, $dateTo = null, $limit = 30) {
        if (!$dateFrom) {
            $dateFrom = date('Y-m-d', strtotime("-{$limit} days"));
        }
        if (!$dateTo) {
            $dateTo = date('Y-m-d');
        }
        
        $sql = "SELECT * FROM daily_visit_stats 
                WHERE visit_date BETWEEN ? AND ? 
                ORDER BY visit_date DESC";
        
        return $this->db->fetchAll($sql, [$dateFrom, $dateTo]);
    }
    
    /**
     * 获取今日统计
     */
    public function getTodayStats() {
        $today = date('Y-m-d');
        
        $sql = "SELECT * FROM daily_visit_stats WHERE visit_date = ?";
        $result = $this->db->fetchRow($sql, [$today]);
        
        if (!$result) {
            return [
                'visit_date' => $today,
                'total_visits' => 0,
                'unique_visitors' => 0,
                'page_views' => 0,
                'new_visitors' => 0,
                'bounce_rate' => 0,
                'avg_session_duration' => 0,
                'mobile_visits' => 0,
                'desktop_visits' => 0,
                'tablet_visits' => 0
            ];
        }
        
        return $result;
    }
    
    /**
     * 获取总体统计
     */
    public function getTotalStats($days = 30) {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        $dateTo = date('Y-m-d');
        
        $sql = "SELECT 
                    SUM(total_visits) as total_visits,
                    SUM(unique_visitors) as total_unique_visitors,
                    SUM(page_views) as total_page_views,
                    SUM(new_visitors) as total_new_visitors,
                    AVG(bounce_rate) as avg_bounce_rate,
                    AVG(avg_session_duration) as avg_session_duration,
                    COUNT(*) as active_days,
                    SUM(mobile_visits) as total_mobile_visits,
                    SUM(desktop_visits) as total_desktop_visits,
                    SUM(tablet_visits) as total_tablet_visits
                FROM daily_visit_stats 
                WHERE visit_date BETWEEN ? AND ?";
        
        $result = $this->db->fetchRow($sql, [$dateFrom, $dateTo]);
        
        return [
            'total_visits' => $result['total_visits'] ?: 0,
            'total_unique_visitors' => $result['total_unique_visitors'] ?: 0,
            'total_page_views' => $result['total_page_views'] ?: 0,
            'total_new_visitors' => $result['total_new_visitors'] ?: 0,
            'avg_bounce_rate' => round($result['avg_bounce_rate'] ?: 0, 2),
            'avg_session_duration' => round($result['avg_session_duration'] ?: 0),
            'active_days' => $result['active_days'] ?: 0,
            'avg_visits_per_day' => $result['active_days'] > 0 ? round($result['total_visits'] / $result['active_days'], 2) : 0,
            'total_mobile_visits' => $result['total_mobile_visits'] ?: 0,
            'total_desktop_visits' => $result['total_desktop_visits'] ?: 0,
            'total_tablet_visits' => $result['total_tablet_visits'] ?: 0
        ];
    }
    
    /**
     * 获取热门页面
     */
    public function getTopPages($dateFrom = null, $dateTo = null, $limit = 10) {
        if (!$dateFrom) {
            $dateFrom = date('Y-m-d', strtotime('-7 days'));
        }
        if (!$dateTo) {
            $dateTo = date('Y-m-d');
        }
        
        $sql = "SELECT 
                    page_url,
                    page_title,
                    SUM(visit_count) as total_visits,
                    SUM(unique_visitors) as total_unique_visitors,
                    AVG(avg_stay_time) as avg_stay_time
                FROM page_visit_stats 
                WHERE visit_date BETWEEN ? AND ?
                GROUP BY page_url, page_title
                ORDER BY total_visits DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$dateFrom, $dateTo, $limit]);
    }
    
    /**
     * 获取访问趋势数据
     */
    public function getVisitTrend($days = 7) {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        $dateTo = date('Y-m-d');
        
        $sql = "SELECT 
                    visit_date,
                    total_visits,
                    unique_visitors,
                    page_views,
                    mobile_visits,
                    desktop_visits,
                    tablet_visits
                FROM daily_visit_stats 
                WHERE visit_date BETWEEN ? AND ?
                ORDER BY visit_date ASC";
        
        $data = $this->db->fetchAll($sql, [$dateFrom, $dateTo]);
        
        // 填充缺失的日期
        $result = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $found = false;
            
            foreach ($data as $row) {
                if ($row['visit_date'] === $date) {
                    $result[] = $row;
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $result[] = [
                    'visit_date' => $date,
                    'total_visits' => 0,
                    'unique_visitors' => 0,
                    'page_views' => 0,
                    'mobile_visits' => 0,
                    'desktop_visits' => 0,
                    'tablet_visits' => 0
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * 获取浏览器统计
     */
    public function getBrowserStats($days = 7) {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        
        $sql = "SELECT 
                    browser,
                    COUNT(*) as visit_count,
                    COUNT(DISTINCT visitor_id) as unique_visitors
                FROM visit_logs 
                WHERE DATE(visit_time) >= ?
                GROUP BY browser
                ORDER BY visit_count DESC
                LIMIT 10";
        
        return $this->db->fetchAll($sql, [$dateFrom]);
    }
    
    /**
     * 获取操作系统统计
     */
    public function getOSStats($days = 7) {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        
        $sql = "SELECT 
                    os,
                    COUNT(*) as visit_count,
                    COUNT(DISTINCT visitor_id) as unique_visitors
                FROM visit_logs 
                WHERE DATE(visit_time) >= ?
                GROUP BY os
                ORDER BY visit_count DESC
                LIMIT 10";
        
        return $this->db->fetchAll($sql, [$dateFrom]);
    }
    
    /**
     * 获取设备类型统计
     */
    public function getDeviceStats($days = 7) {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        
        $sql = "SELECT 
                    device_type,
                    COUNT(*) as visit_count,
                    COUNT(DISTINCT visitor_id) as unique_visitors
                FROM visit_logs 
                WHERE DATE(visit_time) >= ?
                GROUP BY device_type
                ORDER BY visit_count DESC";
        
        return $this->db->fetchAll($sql, [$dateFrom]);
    }
    
    /**
     * 获取最近访问记录
     */
    public function getRecentVisits($limit = 20) {
        $sql = "SELECT 
                    visit_time,
                    page_url,
                    page_title,
                    ip_address,
                    browser,
                    os,
                    device_type,
                    referer_url
                FROM visit_logs 
                ORDER BY visit_time DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    /**
     * 获取访问来源统计
     */
    public function getReferrerStats($days = 7, $limit = 10) {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        
        $sql = "SELECT 
                    referer_domain,
                    referer_type,
                    SUM(visit_count) as total_visits,
                    SUM(unique_visitors) as total_unique_visitors
                FROM referer_stats 
                WHERE visit_date >= ?
                GROUP BY referer_domain, referer_type
                ORDER BY total_visits DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$dateFrom, $limit]);
    }
    
    /**
     * 获取小时访问分布
     */
    public function getHourlyStats($date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        $sql = "SELECT 
                    HOUR(visit_time) as hour,
                    COUNT(*) as visit_count
                FROM visit_logs 
                WHERE DATE(visit_time) = ?
                GROUP BY HOUR(visit_time)
                ORDER BY hour";
        
        $data = $this->db->fetchAll($sql, [$date]);
        
        // 填充24小时数据
        $result = [];
        for ($i = 0; $i < 24; $i++) {
            $found = false;
            foreach ($data as $row) {
                if ($row['hour'] == $i) {
                    $result[] = $row;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $result[] = ['hour' => $i, 'visit_count' => 0];
            }
        }
        
        return $result;
    }
    
    /**
     * 获取实时在线用户数
     */
    public function getOnlineUsers($minutes = 5) {
        $cutoffTime = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));
        
        $sql = "SELECT COUNT(DISTINCT visitor_id) as online_users
                FROM visit_logs 
                WHERE visit_time >= ?";
        
        $result = $this->db->fetchRow($sql, [$cutoffTime]);
        return $result['online_users'] ?: 0;
    }
    
    /**
     * 获取地理位置统计
     */
    public function getLocationStats($days = 7) {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        
        $sql = "SELECT 
                    country,
                    city,
                    COUNT(*) as visit_count,
                    COUNT(DISTINCT visitor_id) as unique_visitors
                FROM visit_logs 
                WHERE DATE(visit_time) >= ? AND country != ''
                GROUP BY country, city
                ORDER BY visit_count DESC
                LIMIT 20";
        
        return $this->db->fetchAll($sql, [$dateFrom]);
    }
    
    /**
     * 获取搜索关键词统计
     */
    public function getSearchKeywords($days = 7) {
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        
        $sql = "SELECT 
                    referer_url,
                    COUNT(*) as visit_count
                FROM visit_logs 
                WHERE DATE(visit_time) >= ? 
                AND referer_url LIKE '%google%' 
                OR referer_url LIKE '%baidu%'
                OR referer_url LIKE '%bing%'
                GROUP BY referer_url
                ORDER BY visit_count DESC
                LIMIT 20";
        
        $data = $this->db->fetchAll($sql, [$dateFrom]);
        
        // 提取搜索关键词
        $keywords = [];
        foreach ($data as $row) {
            $keyword = $this->extractSearchKeyword($row['referer_url']);
            if ($keyword) {
                $keywords[] = [
                    'keyword' => $keyword,
                    'visit_count' => $row['visit_count']
                ];
            }
        }
        
        return $keywords;
    }
    
    /**
     * 从搜索引擎URL中提取关键词
     */
    private function extractSearchKeyword($url) {
        $patterns = [
            '/[?&]q=([^&]+)/',      // Google
            '/[?&]wd=([^&]+)/',     // Baidu
            '/[?&]query=([^&]+)/'   // Bing
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return urldecode($matches[1]);
            }
        }
        
        return null;
    }
}
