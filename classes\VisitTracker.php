<?php
/**
 * 访问统计核心类
 * 负责记录和统计网站访问数据
 */

require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/UserAgent.php';

class VisitTracker {
    private $db;
    private $userAgent;
    private $visitorId;
    private $sessionId;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->userAgent = new UserAgent();
        $this->loadConfig();
        $this->initVisitor();
    }
    
    /**
     * 加载系统配置
     */
    private function loadConfig() {
        $this->config = [
            'track_enabled' => $this->db->getConfig('track_enabled', '1'),
            'exclude_ips' => explode(',', $this->db->getConfig('exclude_ips', '127.0.0.1,::1')),
            'exclude_bots' => $this->db->getConfig('exclude_bots', '1'),
            'session_timeout' => $this->db->getConfig('session_timeout', '1800'),
            'data_retention_days' => $this->db->getConfig('data_retention_days', '365')
        ];
    }
    
    /**
     * 初始化访客信息
     */
    private function initVisitor() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        $this->sessionId = session_id();
        $this->visitorId = $this->generateVisitorId();
    }
    
    /**
     * 生成访客唯一标识
     */
    private function generateVisitorId() {
        // 检查cookie中的访客ID
        if (isset($_COOKIE['visitor_id'])) {
            return $_COOKIE['visitor_id'];
        }
        
        // 生成新的访客ID
        $ip = $this->getClientIp();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '';
        $visitorId = md5($ip . $userAgent . time() . rand(1000, 9999));
        
        // 设置cookie，有效期30天
        setcookie('visitor_id', $visitorId, time() + (30 * 24 * 60 * 60), '/');
        
        return $visitorId;
    }
    
    /**
     * 记录访问
     */
    public function recordVisit($pageUrl = null, $pageTitle = '', $refererUrl = null) {
        // 检查是否启用统计
        if ($this->config['track_enabled'] != '1') {
            return false;
        }
        
        // 获取页面信息
        if ($pageUrl === null) {
            $pageUrl = $this->getCurrentPageUrl();
        }
        
        if ($refererUrl === null) {
            $refererUrl = $_SERVER['HTTP_REFERER'] ? $_SERVER['HTTP_REFERER'] : '';
        }
        
        // 检查是否为有效访问
        if (!$this->isValidVisit($pageUrl)) {
            return false;
        }
        
        try {
            $this->db->beginTransaction();
            
            // 记录访问日志
            $this->recordVisitLog($pageUrl, $pageTitle, $refererUrl);
            
            // 更新访客信息
            $this->updateVisitorInfo();
            
            // 更新每日统计
            $this->updateDailyStats();
            
            // 更新页面统计
            $this->updatePageStats($pageUrl, $pageTitle);
            
            // 更新来源统计
            $this->updateRefererStats($refererUrl);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("访问记录失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查是否为有效访问
     */
    private function isValidVisit($pageUrl) {
        $ip = $this->getClientIp();
        
        // 检查IP是否在排除列表中
        if (in_array($ip, $this->config['exclude_ips'])) {
            return false;
        }
        
        // 检查是否为爬虫
        if ($this->config['exclude_bots'] == '1' && $this->isBot()) {
            return false;
        }
        
        // 排除静态资源
        $excludeExtensions = ['css', 'js', 'jpg', 'jpeg', 'png', 'gif', 'ico', 'svg', 'woff', 'ttf', 'pdf', 'zip'];
        $extension = strtolower(pathinfo(parse_url($pageUrl, PHP_URL_PATH), PATHINFO_EXTENSION));
        
        if (in_array($extension, $excludeExtensions)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查是否为爬虫
     */
    private function isBot() {
        $userAgent = strtolower($_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '');
        $bots = ['bot', 'crawler', 'spider', 'scraper', 'curl', 'wget', 'googlebot', 'bingbot', 'slurp', 'baiduspider'];
        
        foreach ($bots as $bot) {
            if (strpos($userAgent, $bot) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 记录访问日志
     */
    private function recordVisitLog($pageUrl, $pageTitle, $refererUrl) {
        $userAgentInfo = $this->userAgent->parse($_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '');
        
        $data = [
            'visitor_id' => $this->visitorId,
            'session_id' => $this->sessionId,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '',
            'page_url' => $pageUrl,
            'page_title' => $pageTitle,
            'referer_url' => $refererUrl,
            'browser' => $userAgentInfo['browser'],
            'os' => $userAgentInfo['os'],
            'device_type' => $userAgentInfo['device_type'],
            'language' => $this->getBrowserLanguage(),
            'is_new_visitor' => $this->isNewVisitor() ? 1 : 0,
            'visit_time' => date('Y-m-d H:i:s')
        ];
        
        return $this->db->insert('visit_logs', $data);
    }
    
    /**
     * 更新访客信息
     */
    private function updateVisitorInfo() {
        $sql = "SELECT id FROM visitors WHERE visitor_id = ?";
        $visitor = $this->db->fetchRow($sql, [$this->visitorId]);
        
        if ($visitor) {
            // 更新现有访客
            $sql = "UPDATE visitors SET 
                    last_visit = NOW(), 
                    visit_count = visit_count + 1, 
                    page_views = page_views + 1,
                    updated_at = NOW()
                    WHERE visitor_id = ?";
            $this->db->query($sql, [$this->visitorId]);
        } else {
            // 新访客
            $userAgentInfo = $this->userAgent->parse($_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '');
            
            $data = [
                'visitor_id' => $this->visitorId,
                'first_visit' => date('Y-m-d H:i:s'),
                'last_visit' => date('Y-m-d H:i:s'),
                'visit_count' => 1,
                'page_views' => 1,
                'ip_address' => $this->getClientIp(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '',
                'browser' => $userAgentInfo['browser'],
                'os' => $userAgentInfo['os'],
                'device_type' => $userAgentInfo['device_type']
            ];
            
            $this->db->insert('visitors', $data);
        }
    }
    
    /**
     * 更新每日统计
     */
    private function updateDailyStats() {
        $today = date('Y-m-d');
        $deviceType = $this->userAgent->parse($_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '')['device_type'];
        
        $sql = "SELECT id FROM daily_visit_stats WHERE visit_date = ?";
        $stats = $this->db->fetchRow($sql, [$today]);
        
        if ($stats) {
            // 更新现有统计
            $deviceField = $deviceType . '_visits';
            $sql = "UPDATE daily_visit_stats SET 
                    total_visits = total_visits + 1,
                    page_views = page_views + 1,
                    new_visitors = new_visitors + ?,
                    {$deviceField} = {$deviceField} + 1,
                    updated_at = NOW()
                    WHERE visit_date = ?";
            $this->db->query($sql, [$this->isNewVisitor() ? 1 : 0, $today]);
        } else {
            // 创建新统计记录
            $data = [
                'visit_date' => $today,
                'total_visits' => 1,
                'unique_visitors' => 1,
                'page_views' => 1,
                'new_visitors' => $this->isNewVisitor() ? 1 : 0,
                'mobile_visits' => $deviceType === 'mobile' ? 1 : 0,
                'desktop_visits' => $deviceType === 'desktop' ? 1 : 0,
                'tablet_visits' => $deviceType === 'tablet' ? 1 : 0
            ];
            
            $this->db->insert('daily_visit_stats', $data);
        }
        
        // 更新独立访客数
        $this->updateUniqueVisitors($today);
    }
    
    /**
     * 更新页面统计
     */
    private function updatePageStats($pageUrl, $pageTitle) {
        $today = date('Y-m-d');
        
        $sql = "SELECT id FROM page_visit_stats WHERE visit_date = ? AND page_url = ?";
        $stats = $this->db->fetchRow($sql, [$today, $pageUrl]);
        
        if ($stats) {
            // 更新现有统计
            $sql = "UPDATE page_visit_stats SET 
                    visit_count = visit_count + 1,
                    updated_at = NOW()
                    WHERE visit_date = ? AND page_url = ?";
            $this->db->query($sql, [$today, $pageUrl]);
        } else {
            // 创建新统计记录
            $data = [
                'visit_date' => $today,
                'page_url' => $pageUrl,
                'page_title' => $pageTitle,
                'visit_count' => 1,
                'unique_visitors' => 1
            ];
            
            $this->db->insert('page_visit_stats', $data);
        }
        
        // 更新页面独立访客数
        $this->updatePageUniqueVisitors($today, $pageUrl);
    }
    
    /**
     * 更新来源统计
     */
    private function updateRefererStats($refererUrl) {
        if (empty($refererUrl)) {
            $refererDomain = 'direct';
            $refererType = 'direct';
        } else {
            $refererDomain = parse_url($refererUrl, PHP_URL_HOST) ?: 'unknown';
            $refererType = $this->getRefererType($refererUrl);
        }
        
        $today = date('Y-m-d');
        
        $sql = "SELECT id FROM referer_stats WHERE visit_date = ? AND referer_domain = ?";
        $stats = $this->db->fetchRow($sql, [$today, $refererDomain]);
        
        if ($stats) {
            $sql = "UPDATE referer_stats SET 
                    visit_count = visit_count + 1,
                    updated_at = NOW()
                    WHERE visit_date = ? AND referer_domain = ?";
            $this->db->query($sql, [$today, $refererDomain]);
        } else {
            $data = [
                'visit_date' => $today,
                'referer_domain' => $refererDomain,
                'referer_type' => $refererType,
                'visit_count' => 1,
                'unique_visitors' => 1
            ];
            
            $this->db->insert('referer_stats', $data);
        }
    }
    
    /**
     * 获取来源类型
     */
    private function getRefererType($refererUrl) {
        $domain = strtolower(parse_url($refererUrl, PHP_URL_HOST) ?: '');
        
        // 搜索引擎
        $searchEngines = ['google', 'baidu', 'bing', 'yahoo', 'sogou', 'so.com'];
        foreach ($searchEngines as $engine) {
            if (strpos($domain, $engine) !== false) {
                return 'search';
            }
        }
        
        // 社交媒体
        $socialSites = ['facebook', 'twitter', 'weibo', 'qq.com', 'wechat'];
        foreach ($socialSites as $social) {
            if (strpos($domain, $social) !== false) {
                return 'social';
            }
        }
        
        return 'referral';
    }
    
    /**
     * 更新独立访客数
     */
    private function updateUniqueVisitors($date) {
        $sql = "SELECT COUNT(DISTINCT visitor_id) as unique_count 
                FROM visit_logs 
                WHERE DATE(visit_time) = ?";
        $result = $this->db->fetchRow($sql, [$date]);
        
        if ($result) {
            $sql = "UPDATE daily_visit_stats SET unique_visitors = ? WHERE visit_date = ?";
            $this->db->query($sql, [$result['unique_count'], $date]);
        }
    }
    
    /**
     * 更新页面独立访客数
     */
    private function updatePageUniqueVisitors($date, $pageUrl) {
        $sql = "SELECT COUNT(DISTINCT visitor_id) as unique_count 
                FROM visit_logs 
                WHERE DATE(visit_time) = ? AND page_url = ?";
        $result = $this->db->fetchRow($sql, [$date, $pageUrl]);
        
        if ($result) {
            $sql = "UPDATE page_visit_stats SET unique_visitors = ? WHERE visit_date = ? AND page_url = ?";
            $this->db->query($sql, [$result['unique_count'], $date, $pageUrl]);
        }
    }
    
    /**
     * 检查是否为新访客
     */
    private function isNewVisitor() {
        return !isset($_COOKIE['visitor_id']);
    }
    
    /**
     * 获取当前页面URL
     */
    private function getCurrentPageUrl() {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'] ? $_SERVER['HTTP_HOST'] : 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ? $_SERVER['REQUEST_URI'] : '/';
        
        return $protocol . $host . $uri;
    }
    
    /**
     * 获取客户端IP地址
     */
    private function getClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * 获取浏览器语言
     */
    private function getBrowserLanguage() {
        $language = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ? $_SERVER['HTTP_ACCEPT_LANGUAGE'] : '';
        if (!empty($language)) {
            $language = substr($language, 0, 2);
        }
        return $language ?: 'en';
    }
    
    /**
     * 清理过期数据
     */
    public function cleanupOldData() {
        $retentionDays = $this->config['data_retention_days'];
        $cutoffDate = date('Y-m-d', strtotime("-{$retentionDays} days"));
        
        $tables = ['visit_logs', 'daily_visit_stats', 'page_visit_stats', 'referer_stats'];
        
        foreach ($tables as $table) {
            $dateField = ($table === 'visit_logs') ? 'visit_time' : 'visit_date';
            $sql = "DELETE FROM {$table} WHERE {$dateField} < ?";
            $this->db->query($sql, [$cutoffDate]);
        }
    }
}
