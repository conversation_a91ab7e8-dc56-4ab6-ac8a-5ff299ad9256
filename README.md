# 📊 PHP网站访问流量统计系统

一个功能完整的PHP网站访问流量统计系统，支持实时统计访问量并存储到MySQL数据库中。

## ✨ 主要特性

- 🎯 **精准统计** - 实时记录访问量、独立访客、页面浏览量等关键指标
- 📈 **趋势分析** - 提供访问趋势图表，支持多维度数据分析
- 🔍 **详细报表** - 热门页面排行、浏览器统计、设备分析等详细报表
- ⚡ **高性能** - 优化的数据库设计，支持高并发访问
- 🤖 **智能过滤** - 自动识别和过滤搜索引擎爬虫、恶意访问
- 📱 **设备分析** - 统计访客使用的设备类型、浏览器、操作系统
- 🔥 **热门页面** - 统计最受欢迎的页面
- ⏰ **实时监控** - 实时记录访问数据，支持在线用户数统计
- 🌐 **来源分析** - 分析访问来源，包括搜索引擎、社交媒体等

## 🛠️ 系统要求

- **PHP版本**: 7.0 或更高版本
- **数据库**: MySQL 5.7 或更高版本
- **PHP扩展**: PDO, PDO_MySQL
- **权限**: 文件读写权限，数据库操作权限
- **存储空间**: 建议至少100MB可用空间

## 📁 文件结构

```
├── config/
│   └── database.php          # 数据库配置文件
├── classes/
│   ├── Database.php          # 数据库操作类
│   ├── VisitTracker.php      # 访问统计核心类
│   ├── UserAgent.php         # 用户代理解析类
│   └── StatsService.php      # 统计数据服务类
├── database/
│   └── visit_stats.sql       # 数据库表结构
├── track.php                 # 访问统计入口文件
├── dashboard.php             # 统计仪表板
├── demo.php                  # 演示页面
├── install.php               # 安装脚本
└── README.md                 # 说明文档
```

## 🚀 快速开始

### 1. 安装系统

访问 `install.php` 运行安装向导：

```
http://yoursite.com/install.php
```

安装向导将引导您完成以下步骤：
1. 系统要求检查
2. 数据库配置
3. 数据库表创建

### 2. 集成统计功能

在需要统计访问量的页面中添加以下代码：

```php
<?php
// 设置页面标题（可选）
$page_title = '您的页面标题';

// 引入访问统计
require_once 'track.php';
?>
```

### 3. 查看统计数据

访问统计仪表板查看详细的访问数据：

```
http://yoursite.com/dashboard.php
```

## 📊 数据库表结构

系统包含以下6个数据表：

### 1. visit_logs - 访问日志表
记录每次访问的详细信息，包括访客ID、IP地址、页面URL、浏览器信息等。

### 2. daily_visit_stats - 每日访问统计表
按天汇总的访问统计数据，包括总访问量、独立访客数、页面浏览量等。

### 3. page_visit_stats - 页面访问统计表
按页面统计的访问情况，包括访问次数、独立访客数、平均停留时间等。

### 4. visitors - 访客信息表
存储访客的基本信息，包括首次访问时间、访问次数、设备信息等。

### 5. referer_stats - 来源统计表
统计访问来源，包括搜索引擎、社交媒体、直接访问等。

### 6. visit_config - 系统配置表
存储系统配置信息，如是否启用统计、排除IP列表等。

## ⚙️ 配置选项

系统支持以下配置选项（存储在 `visit_config` 表中）：

- `track_enabled` - 是否启用统计（1=启用，0=禁用）
- `exclude_ips` - 排除的IP地址列表（逗号分隔）
- `exclude_bots` - 是否排除爬虫（1=排除，0=不排除）
- `session_timeout` - 会话超时时间（秒）
- `data_retention_days` - 数据保留天数
- `enable_geolocation` - 是否启用地理位置识别
- `timezone` - 时区设置

## 🔧 高级用法

### 自定义页面标题

```php
<?php
$page_title = '自定义页面标题';
require_once 'track.php';
?>
```

### 获取统计数据

```php
<?php
require_once 'classes/StatsService.php';

$statsService = new StatsService();

// 获取今日统计
$todayStats = $statsService->getTodayStats();

// 获取访问趋势
$visitTrend = $statsService->getVisitTrend(7);

// 获取热门页面
$topPages = $statsService->getTopPages();

// 获取在线用户数
$onlineUsers = $statsService->getOnlineUsers(5);
?>
```

### 手动记录访问

```php
<?php
require_once 'classes/VisitTracker.php';

$tracker = new VisitTracker();
$tracker->recordVisit($pageUrl, $pageTitle, $refererUrl);
?>
```

## 🎯 功能特性详解

### 访客识别
- 基于Cookie的访客唯一标识
- IP地址辅助识别
- 30天Cookie有效期

### 爬虫过滤
- 自动识别常见搜索引擎爬虫
- 用户代理字符串分析
- 可配置的爬虫过滤规则

### 数据统计
- 实时访问记录
- 按天聚合统计
- 独立访客去重
- 页面访问排行
- 在线用户统计

### 性能优化
- 数据库索引优化
- 事务处理保证数据一致性
- 异常处理不影响页面正常显示

## 🔒 安全特性

- PDO预处理语句防止SQL注入
- 输入数据验证和过滤
- 错误日志记录
- 配置文件权限保护

## 📈 统计指标说明

- **访问量** - 页面被访问的总次数
- **独立访客** - 基于Cookie和IP识别的唯一访客数
- **页面浏览量** - 所有页面被浏览的总次数
- **新访客** - 首次访问网站的访客数
- **在线用户** - 最近5分钟内有访问行为的用户数
- **跳出率** - 只访问一个页面就离开的访客比例
- **平均会话时长** - 访客在网站停留的平均时间

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务正常运行
   - 验证用户权限

2. **访问数据未记录**
   - 检查 `track.php` 是否正确引入
   - 查看错误日志
   - 确认数据库表是否创建成功

3. **统计页面显示异常**
   - 检查PHP错误日志
   - 确认所有类文件存在
   - 验证数据库连接

### 调试模式

在开发环境中，可以启用错误显示：

```php
<?php
// 在页面顶部添加
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
```

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 基础访问统计功能
- 数据库存储支持
- 统计仪表板
- 安装向导
- 实时在线用户统计
- 来源分析功能

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统要求是否满足
3. 查看错误日志获取详细信息

---

**开始使用这个强大的访问统计系统，获得详细的网站访问分析数据！** 🚀
