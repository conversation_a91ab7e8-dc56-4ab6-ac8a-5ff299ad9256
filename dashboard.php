<?php
/**
 * 访问统计仪表板
 */

require_once __DIR__ . '/classes/StatsService.php';

// 记录当前页面访问
$page_title = '访问统计仪表板';
require_once __DIR__ . '/track.php';

// 创建统计服务实例
$statsService = new StatsService();

// 获取统计数据
$todayStats = $statsService->getTodayStats();
$totalStats = $statsService->getTotalStats(30);
$visitTrend = $statsService->getVisitTrend(7);
$topPages = $statsService->getTopPages(null, null, 10);
$browserStats = $statsService->getBrowserStats(7);
$deviceStats = $statsService->getDeviceStats(7);
$recentVisits = $statsService->getRecentVisits(15);
$referrerStats = $statsService->getReferrerStats(7, 10);
$onlineUsers = $statsService->getOnlineUsers(5);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站访问统计仪表板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f7fa; color: #333; line-height: 1.6; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.1em; opacity: 0.9; }
        
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; position: relative; overflow: hidden; transition: transform 0.2s; }
        .stat-card:hover { transform: translateY(-2px); }
        .stat-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #3498db, #2ecc71); }
        .stat-number { font-size: 2.5em; font-weight: bold; color: #2c3e50; margin-bottom: 5px; }
        .stat-label { color: #7f8c8d; font-size: 1em; text-transform: uppercase; letter-spacing: 1px; }
        .stat-icon { font-size: 1.5em; margin-bottom: 10px; }
        
        .section { background: white; padding: 30px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .section-title { font-size: 1.5em; color: #2c3e50; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #ecf0f1; display: flex; align-items: center; gap: 10px; }
        
        .chart-container { height: 300px; margin: 20px 0; position: relative; }
        .trend-chart { display: flex; align-items: end; height: 250px; gap: 8px; padding: 20px 0; }
        .trend-bar { background: linear-gradient(to top, #3498db, #5dade2); border-radius: 4px 4px 0 0; min-height: 5px; flex: 1; position: relative; display: flex; align-items: end; justify-content: center; transition: all 0.3s; }
        .trend-bar:hover { background: linear-gradient(to top, #2980b9, #3498db); transform: scale(1.05); }
        .bar-value { color: white; font-size: 12px; font-weight: bold; padding: 4px; }
        .bar-label { position: absolute; bottom: -25px; font-size: 11px; color: #7f8c8d; transform: rotate(-45deg); transform-origin: center; }
        
        .table-container { overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #ecf0f1; }
        th { background: #f8f9fa; font-weight: 600; color: #2c3e50; }
        tr:hover { background: #f8f9fa; }
        
        .progress-bar { background: #ecf0f1; height: 8px; border-radius: 4px; overflow: hidden; margin-top: 5px; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #3498db, #2ecc71); border-radius: 4px; transition: width 0.3s ease; }
        
        .device-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .device-card { text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; transition: transform 0.2s; }
        .device-card:hover { transform: translateY(-2px); }
        .device-icon { font-size: 2em; margin-bottom: 10px; }
        .device-count { font-size: 1.5em; font-weight: bold; color: #2c3e50; }
        .device-label { color: #7f8c8d; font-size: 0.9em; }
        
        .refresh-btn { background: linear-gradient(135deg, #3498db, #2ecc71); color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 1em; transition: transform 0.2s; }
        .refresh-btn:hover { transform: translateY(-2px); }
        
        .online-indicator { display: inline-flex; align-items: center; gap: 5px; background: #2ecc71; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.9em; }
        .online-dot { width: 8px; height: 8px; background: white; border-radius: 50%; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; }
        
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .stats-grid { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
            .stat-number { font-size: 2em; }
            .section { padding: 20px; }
            .grid-2 { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>📊 网站访问统计仪表板</h1>
            <p>数据更新时间：<?php echo date('Y-m-d H:i:s'); ?></p>
            <div style="margin-top: 15px;">
                <span class="online-indicator">
                    <span class="online-dot"></span>
                    <?php echo $onlineUsers; ?> 人在线
                </span>
                <button class="refresh-btn" onclick="location.reload()" style="margin-left: 15px;">🔄 刷新数据</button>
            </div>
        </div>

        <!-- 今日统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-number"><?php echo number_format($todayStats['total_visits']); ?></div>
                <div class="stat-label">今日访问量</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-number"><?php echo number_format($todayStats['unique_visitors']); ?></div>
                <div class="stat-label">今日独立访客</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📄</div>
                <div class="stat-number"><?php echo number_format($todayStats['page_views']); ?></div>
                <div class="stat-label">今日页面浏览量</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✨</div>
                <div class="stat-number"><?php echo number_format($todayStats['new_visitors']); ?></div>
                <div class="stat-label">今日新访客</div>
            </div>
        </div>

        <!-- 30天总体统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📈</div>
                <div class="stat-number"><?php echo number_format($totalStats['total_visits']); ?></div>
                <div class="stat-label">30天总访问量</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👤</div>
                <div class="stat-number"><?php echo number_format($totalStats['total_unique_visitors']); ?></div>
                <div class="stat-label">30天独立访客</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-number"><?php echo $totalStats['avg_visits_per_day']; ?></div>
                <div class="stat-label">日均访问量</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📅</div>
                <div class="stat-number"><?php echo $totalStats['active_days']; ?></div>
                <div class="stat-label">活跃天数</div>
            </div>
        </div>

        <!-- 访问趋势图 -->
        <div class="section">
            <h2 class="section-title">📈 最近7天访问趋势</h2>
            <div class="chart-container">
                <div class="trend-chart">
                    <?php
                    $maxVisits = max(array_column($visitTrend, 'total_visits')) ?: 1;
                    foreach ($visitTrend as $day):
                        $height = ($day['total_visits'] / $maxVisits) * 100;
                        $date = date('m/d', strtotime($day['visit_date']));
                        $isToday = $day['visit_date'] === date('Y-m-d');
                    ?>
                        <div class="trend-bar" style="height: <?php echo $height; ?>%; background: <?php echo $isToday ? 'linear-gradient(to top, #e74c3c, #c0392b)' : 'linear-gradient(to top, #3498db, #5dade2)'; ?>;" title="<?php echo $day['visit_date'] . ': ' . $day['total_visits'] . ' 访问'; ?>">
                            <?php if ($day['total_visits'] > 0): ?>
                                <div class="bar-value"><?php echo $day['total_visits']; ?></div>
                            <?php endif; ?>
                            <div class="bar-label"><?php echo $date; ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <div class="grid-2">
            <!-- 热门页面 -->
            <div class="section">
                <h2 class="section-title">🔥 热门页面</h2>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>页面</th>
                                <th>访问次数</th>
                                <th>独立访客</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $rank = 1;
                            foreach (array_slice($topPages, 0, 8) as $page): 
                            ?>
                                <tr>
                                    <td><?php echo $rank++; ?></td>
                                    <td>
                                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="<?php echo htmlspecialchars($page['page_url']); ?>">
                                            <?php echo htmlspecialchars($page['page_title'] ?: $page['page_url']); ?>
                                        </div>
                                    </td>
                                    <td><?php echo number_format($page['total_visits']); ?></td>
                                    <td><?php echo number_format($page['total_unique_visitors']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 访问来源 -->
            <div class="section">
                <h2 class="section-title">🌐 访问来源</h2>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>来源</th>
                                <th>类型</th>
                                <th>访问次数</th>
                                <th>占比</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $totalReferrerVisits = array_sum(array_column($referrerStats, 'total_visits'));
                            foreach (array_slice($referrerStats, 0, 8) as $referrer): 
                                $percentage = $totalReferrerVisits > 0 ? round(($referrer['total_visits'] / $totalReferrerVisits) * 100, 1) : 0;
                            ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($referrer['referer_domain']); ?></td>
                                    <td>
                                        <span style="background: #3498db; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">
                                            <?php echo ucfirst($referrer['referer_type']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($referrer['total_visits']); ?></td>
                                    <td>
                                        <?php echo $percentage; ?>%
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: <?php echo $percentage; ?>%;"></div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 设备统计 -->
        <div class="section">
            <h2 class="section-title">📱 设备类型统计</h2>
            <div class="device-stats">
                <?php foreach ($deviceStats as $device): ?>
                    <div class="device-card">
                        <div class="device-icon">
                            <?php 
                            switch($device['device_type']) {
                                case 'mobile': echo '📱'; break;
                                case 'tablet': echo '📱'; break;
                                case 'desktop': echo '💻'; break;
                                default: echo '🖥️';
                            }
                            ?>
                        </div>
                        <div class="device-count"><?php echo number_format($device['visit_count']); ?></div>
                        <div class="device-label"><?php echo ucfirst($device['device_type']); ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="grid-2">
            <!-- 浏览器统计 -->
            <div class="section">
                <h2 class="section-title">🌐 浏览器统计</h2>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>浏览器</th>
                                <th>访问次数</th>
                                <th>占比</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $totalBrowserVisits = array_sum(array_column($browserStats, 'visit_count'));
                            foreach ($browserStats as $browser): 
                                $percentage = $totalBrowserVisits > 0 ? round(($browser['visit_count'] / $totalBrowserVisits) * 100, 1) : 0;
                            ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($browser['browser']); ?></td>
                                    <td><?php echo number_format($browser['visit_count']); ?></td>
                                    <td>
                                        <?php echo $percentage; ?>%
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: <?php echo $percentage; ?>%;"></div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 最近访问记录 -->
            <div class="section">
                <h2 class="section-title">🕒 最近访问记录</h2>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>页面</th>
                                <th>设备</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($recentVisits, 0, 10) as $visit): ?>
                                <tr>
                                    <td><?php echo date('H:i', strtotime($visit['visit_time'])); ?></td>
                                    <td>
                                        <div style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="<?php echo htmlspecialchars($visit['page_url']); ?>">
                                            <?php echo htmlspecialchars($visit['page_title'] ?: basename($visit['page_url'])); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php 
                                        switch($visit['device_type']) {
                                            case 'mobile': echo '📱'; break;
                                            case 'tablet': echo '📱'; break;
                                            default: echo '💻';
                                        }
                                        echo ' ' . htmlspecialchars($visit['browser']);
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 页面底部 -->
        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📊 网站访问统计系统 |
               <a href="demo.php" style="color: #3498db;">查看演示页面</a> |
               <a href="install.php" style="color: #3498db;">系统安装</a>
            </p>
        </div>
    </div>

    <script>
        // 自动刷新在线用户数
        function updateOnlineUsers() {
            // 这里可以通过AJAX获取实时在线用户数
            // 为了演示，暂时不实现
        }

        // 每30秒更新一次在线用户数
        setInterval(updateOnlineUsers, 30000);
    </script>
</body>
</html>
