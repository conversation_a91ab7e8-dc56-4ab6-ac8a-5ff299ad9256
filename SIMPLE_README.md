# 简单PHP网站访问量统计

一个轻量级的PHP网站访问量统计系统，使用文件存储数据，无需数据库配置。

## ✨ 特性

- 🚀 **轻量级** - 单文件实现，代码简洁
- 📁 **无数据库** - 使用JSON文件存储，部署简单
- 🔄 **实时统计** - 访问即时记录和显示
- 📊 **可视化** - 内置图表和统计界面
- 🤖 **智能过滤** - 自动过滤爬虫和无效访问
- 🧹 **自动清理** - 自动清理30天前的数据

## 📋 系统要求

- PHP 5.4 或更高版本
- 可写的文件权限

## 🚀 快速开始

### 1. 下载文件
将以下文件上传到您的网站目录：
- `simple_visit_counter.php` - 核心统计功能
- `example.php` - 使用示例
- `stats.php` - 统计查看页面

### 2. 基本使用
在需要统计的页面中添加：

```php
<?php
// 引入统计功能
require_once 'simple_visit_counter.php';

// 记录访问
recordVisit($data_file, $log_file);

// 显示统计（可选）
displayVisitStats($data_file);
?>
```

### 3. 查看统计
访问 `stats.php` 查看详细的访问统计数据。

## 📖 使用示例

### 基本集成
```php
<?php
require_once 'simple_visit_counter.php';
recordVisit($data_file, $log_file);
?>
<!DOCTYPE html>
<html>
<head>
    <title>我的网站</title>
</head>
<body>
    <h1>欢迎访问我的网站</h1>
    
    <!-- 显示访问统计 -->
    <?php displayVisitStats($data_file); ?>
</body>
</html>
```

### 仅记录访问
```php
<?php
require_once 'simple_visit_counter.php';
recordVisit($data_file, $log_file);
// 不显示统计，只记录访问
?>
```

### 获取统计数据
```php
<?php
require_once 'simple_visit_counter.php';

// 获取统计数据
$stats = getVisitStats($data_file);

echo "今日访问量：" . $stats['today']['total_visits'];
echo "今日独立访客：" . $stats['today']['unique_visitors'];
?>
```

## 📊 功能说明

### 统计指标
- **总访问量** - 页面被访问的总次数
- **独立访客** - 基于IP地址统计的独立访客数
- **页面浏览量** - 各个页面的访问次数
- **访问趋势** - 最近几天的访问变化
- **热门页面** - 访问量最高的页面排行

### 数据存储
- `visit_data.txt` - 统计数据（JSON格式）
- `visit_log.txt` - 访问日志

### 自动功能
- 过滤爬虫访问
- 去除重复统计
- 自动清理过期数据
- 实时更新统计

## 🔧 配置选项

可以在 `simple_visit_counter.php` 中修改以下配置：

```php
// 数据文件路径
$data_file = __DIR__ . '/visit_data.txt';
$log_file = __DIR__ . '/visit_log.txt';

// 数据保留天数（在 saveVisitData 函数中）
$cutoff_date = date('Y-m-d', strtotime('-30 days')); // 保留30天
```

## 📁 文件结构

```
├── simple_visit_counter.php    # 核心统计功能
├── example.php                 # 使用示例页面
├── stats.php                   # 统计查看页面
├── visit_data.txt              # 统计数据（自动生成）
├── visit_log.txt               # 访问日志（自动生成）
└── SIMPLE_README.md            # 说明文档
```

## 🎯 核心函数

### recordVisit($data_file, $log_file)
记录当前页面访问

### getVisitStats($data_file, $days = 7)
获取访问统计数据

### displayVisitStats($data_file)
显示访问统计界面

### isValidVisit($ip, $user_agent)
检查是否为有效访问

## 🔍 数据格式

### 统计数据结构
```json
{
  "2025-09-05": {
    "total_visits": 150,
    "unique_visitors": ["***********", "***********"],
    "page_views": {
      "/": 80,
      "/about.php": 35,
      "/contact.php": 35
    }
  }
}
```

### 返回的统计数组
```php
$stats = [
    'today' => [
        'total_visits' => 150,
        'unique_visitors' => 45,
        'page_views' => 200
    ],
    'recent_days' => [
        '2025-09-05' => [...],
        '2025-09-04' => [...]
    ],
    'popular_pages' => [
        '/' => 80,
        '/about.php' => 35
    ]
];
```

## 🛠️ 自定义开发

### 自定义统计显示
```php
<?php
require_once 'simple_visit_counter.php';
$stats = getVisitStats($data_file);
?>

<div class="my-stats">
    <h3>网站统计</h3>
    <p>今日访问：<?php echo $stats['today']['total_visits']; ?></p>
    <p>今日访客：<?php echo $stats['today']['unique_visitors']; ?></p>
</div>
```

### 添加自定义过滤规则
在 `isValidVisit()` 函数中添加：
```php
// 排除特定IP
if ($ip === '*************') {
    return false;
}

// 排除特定页面
if (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
    return false;
}
```

## 🚨 注意事项

1. **文件权限** - 确保PHP有权限创建和写入文件
2. **数据备份** - 定期备份 `visit_data.txt` 文件
3. **性能考虑** - 高访问量网站建议使用数据库方案
4. **隐私保护** - 系统记录IP地址，请遵守相关法律法规

## 📈 性能优化

- 文件会自动清理30天前的数据
- 使用文件锁防止并发写入冲突
- 爬虫访问自动过滤，减少无效数据

## 🔗 相关链接

- [example.php](example.php) - 查看使用示例
- [stats.php](stats.php) - 查看统计数据
- [simple_visit_counter.php](simple_visit_counter.php) - 查看源代码

## 📄 许可证

MIT License - 可自由使用、修改和分发。
