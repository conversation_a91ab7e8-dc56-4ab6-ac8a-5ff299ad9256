<?php
/**
 * 访问统计查看页面
 */

// 引入访问统计功能
require_once __DIR__ . '/simple_visit_counter.php';

// 记录当前页面访问
recordVisit($data_file, $log_file);

// 获取统计数据
$stats = getVisitStats($data_file, 30); // 获取最近30天数据
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站访问统计</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007cba;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007cba;
            margin: 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border-left: 4px solid #007cba;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007cba;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .today-row {
            background-color: #e8f5e8 !important;
        }
        .refresh-btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background: #005a87;
        }
        .chart-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .bar-chart {
            display: flex;
            align-items: end;
            height: 200px;
            gap: 5px;
            margin-top: 10px;
        }
        .bar {
            background: #007cba;
            min-height: 5px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: end;
            align-items: center;
            position: relative;
        }
        .bar-value {
            color: white;
            font-size: 12px;
            padding: 2px;
            font-weight: bold;
        }
        .bar-label {
            position: absolute;
            bottom: -25px;
            font-size: 10px;
            color: #666;
            transform: rotate(-45deg);
            transform-origin: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 网站访问统计</h1>
            <p>数据更新时间：<?php echo date('Y-m-d H:i:s'); ?></p>
            <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="refresh-btn">🔄 刷新数据</a>
        </div>

        <!-- 总体统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today']['total_visits']; ?></div>
                <div class="stat-label">今日访问量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today']['unique_visitors']; ?></div>
                <div class="stat-label">今日独立访客</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today']['page_views']; ?></div>
                <div class="stat-label">今日页面浏览量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_recent']['total_visits']; ?></div>
                <div class="stat-label">最近30天总访问量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_recent']['unique_visitors']; ?></div>
                <div class="stat-label">最近30天独立访客</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($stats['popular_pages']); ?></div>
                <div class="stat-label">活跃页面数</div>
            </div>
        </div>

        <!-- 访问趋势图表 -->
        <div class="section">
            <h2>📈 最近7天访问趋势</h2>
            <div class="chart-container">
                <div class="bar-chart">
                    <?php
                    $recent_7_days = array_slice($stats['recent_days'], 0, 7, true);
                    $max_visits = max(array_column($recent_7_days, 'total_visits')) ?: 1;
                    
                    foreach (array_reverse($recent_7_days, true) as $date => $day_stats):
                        $height = ($day_stats['total_visits'] / $max_visits) * 100;
                        $is_today = ($date === date('Y-m-d'));
                    ?>
                        <div class="bar" style="height: <?php echo $height; ?>%; background-color: <?php echo $is_today ? '#28a745' : '#007cba'; ?>;">
                            <?php if ($day_stats['total_visits'] > 0): ?>
                                <div class="bar-value"><?php echo $day_stats['total_visits']; ?></div>
                            <?php endif; ?>
                            <div class="bar-label"><?php echo date('m/d', strtotime($date)); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="section">
            <h2>📅 每日访问详情</h2>
            <table>
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>总访问量</th>
                        <th>独立访客</th>
                        <th>页面浏览量</th>
                        <th>平均PV/访客</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($stats['recent_days'] as $date => $day_stats): 
                        $is_today = ($date === date('Y-m-d'));
                        $avg_pv = $day_stats['unique_visitors'] > 0 ? round($day_stats['page_views'] / $day_stats['unique_visitors'], 2) : 0;
                    ?>
                        <tr class="<?php echo $is_today ? 'today-row' : ''; ?>">
                            <td><?php echo $date; ?> <?php echo $is_today ? '(今天)' : ''; ?></td>
                            <td><?php echo number_format($day_stats['total_visits']); ?></td>
                            <td><?php echo number_format($day_stats['unique_visitors']); ?></td>
                            <td><?php echo number_format($day_stats['page_views']); ?></td>
                            <td><?php echo $avg_pv; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 热门页面 -->
        <?php if (!empty($stats['popular_pages'])): ?>
        <div class="section">
            <h2>🔥 热门页面排行</h2>
            <table>
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>页面URL</th>
                        <th>访问次数</th>
                        <th>占比</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total_page_views = array_sum($stats['popular_pages']);
                    $rank = 1;
                    foreach ($stats['popular_pages'] as $page => $views): 
                        $percentage = $total_page_views > 0 ? round(($views / $total_page_views) * 100, 1) : 0;
                    ?>
                        <tr>
                            <td><?php echo $rank++; ?></td>
                            <td><?php echo htmlspecialchars($page); ?></td>
                            <td><?php echo number_format($views); ?></td>
                            <td><?php echo $percentage; ?>%</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <!-- 系统信息 -->
        <div class="section">
            <h2>ℹ️ 系统信息</h2>
            <table>
                <tr>
                    <td><strong>数据文件位置</strong></td>
                    <td><?php echo realpath($data_file) ?: $data_file; ?></td>
                </tr>
                <tr>
                    <td><strong>日志文件位置</strong></td>
                    <td><?php echo realpath($log_file) ?: $log_file; ?></td>
                </tr>
                <tr>
                    <td><strong>数据文件大小</strong></td>
                    <td><?php echo file_exists($data_file) ? round(filesize($data_file) / 1024, 2) . ' KB' : '文件不存在'; ?></td>
                </tr>
                <tr>
                    <td><strong>日志文件大小</strong></td>
                    <td><?php echo file_exists($log_file) ? round(filesize($log_file) / 1024, 2) . ' KB' : '文件不存在'; ?></td>
                </tr>
                <tr>
                    <td><strong>数据保留天数</strong></td>
                    <td>30天（自动清理）</td>
                </tr>
            </table>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>📝 <a href="example.php" style="color: #007cba;">返回示例页面</a> | 
               💻 <a href="simple_visit_counter.php" style="color: #007cba;">查看源代码</a></p>
        </div>
    </div>
</body>
</html>
