-- 网站访问统计数据库表结构
-- 创建时间: 2025-01-08

-- 设置字符集
SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 访问日志表 - 记录每次访问的详细信息
DROP TABLE IF EXISTS `visit_logs`;
CREATE TABLE `visit_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客唯一标识',
  `session_id` varchar(128) NOT NULL COMMENT '会话ID',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理字符串',
  `page_url` varchar(500) NOT NULL COMMENT '访问页面URL',
  `page_title` varchar(200) DEFAULT '' COMMENT '页面标题',
  `referer_url` varchar(500) DEFAULT '' COMMENT '来源页面URL',
  `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT '' COMMENT '操作系统',
  `device_type` enum('desktop','mobile','tablet') DEFAULT 'desktop' COMMENT '设备类型',
  `screen_resolution` varchar(20) DEFAULT '' COMMENT '屏幕分辨率',
  `language` varchar(10) DEFAULT '' COMMENT '浏览器语言',
  `country` varchar(50) DEFAULT '' COMMENT '国家',
  `city` varchar(100) DEFAULT '' COMMENT '城市',
  `is_new_visitor` tinyint(1) DEFAULT 0 COMMENT '是否新访客',
  `visit_time` datetime NOT NULL COMMENT '访问时间',
  `stay_time` int(11) DEFAULT 0 COMMENT '页面停留时间(秒)',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_page_url` (`page_url`(255)),
  KEY `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='访问日志表';

-- 2. 每日访问统计表 - 按天汇总的访问统计
DROP TABLE IF EXISTS `daily_visit_stats`;
CREATE TABLE `daily_visit_stats` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_date` date NOT NULL COMMENT '访问日期',
  `total_visits` int(11) DEFAULT 0 COMMENT '总访问量',
  `unique_visitors` int(11) DEFAULT 0 COMMENT '独立访客数',
  `page_views` int(11) DEFAULT 0 COMMENT '页面浏览量',
  `new_visitors` int(11) DEFAULT 0 COMMENT '新访客数',
  `bounce_rate` decimal(5,2) DEFAULT 0.00 COMMENT '跳出率(%)',
  `avg_session_duration` int(11) DEFAULT 0 COMMENT '平均会话时长(秒)',
  `avg_page_views` decimal(8,2) DEFAULT 0.00 COMMENT '平均页面浏览量',
  `mobile_visits` int(11) DEFAULT 0 COMMENT '移动端访问量',
  `desktop_visits` int(11) DEFAULT 0 COMMENT '桌面端访问量',
  `tablet_visits` int(11) DEFAULT 0 COMMENT '平板端访问量',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_visit_date` (`visit_date`),
  KEY `idx_visit_date` (`visit_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='每日访问统计表';

-- 3. 页面访问统计表 - 按页面统计访问情况
DROP TABLE IF EXISTS `page_visit_stats`;
CREATE TABLE `page_visit_stats` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_date` date NOT NULL COMMENT '访问日期',
  `page_url` varchar(500) NOT NULL COMMENT '页面URL',
  `page_title` varchar(200) DEFAULT '' COMMENT '页面标题',
  `visit_count` int(11) DEFAULT 0 COMMENT '访问次数',
  `unique_visitors` int(11) DEFAULT 0 COMMENT '独立访客数',
  `avg_stay_time` int(11) DEFAULT 0 COMMENT '平均停留时间(秒)',
  `bounce_count` int(11) DEFAULT 0 COMMENT '跳出次数',
  `entry_count` int(11) DEFAULT 0 COMMENT '入口页面次数',
  `exit_count` int(11) DEFAULT 0 COMMENT '出口页面次数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_url` (`visit_date`, `page_url`(255)),
  KEY `idx_visit_date` (`visit_date`),
  KEY `idx_page_url` (`page_url`(255)),
  KEY `idx_visit_count` (`visit_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='页面访问统计表';

-- 4. 访客信息表 - 存储访客的基本信息
DROP TABLE IF EXISTS `visitors`;
CREATE TABLE `visitors` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客唯一标识',
  `first_visit` datetime NOT NULL COMMENT '首次访问时间',
  `last_visit` datetime NOT NULL COMMENT '最后访问时间',
  `visit_count` int(11) DEFAULT 1 COMMENT '访问次数',
  `page_views` int(11) DEFAULT 1 COMMENT '页面浏览量',
  `total_stay_time` int(11) DEFAULT 0 COMMENT '总停留时间(秒)',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理字符串',
  `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT '' COMMENT '操作系统',
  `device_type` enum('desktop','mobile','tablet') DEFAULT 'desktop' COMMENT '设备类型',
  `country` varchar(50) DEFAULT '' COMMENT '国家',
  `city` varchar(100) DEFAULT '' COMMENT '城市',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_visitor_id` (`visitor_id`),
  KEY `idx_first_visit` (`first_visit`),
  KEY `idx_last_visit` (`last_visit`),
  KEY `idx_ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='访客信息表';

-- 5. 来源统计表 - 统计访问来源
DROP TABLE IF EXISTS `referer_stats`;
CREATE TABLE `referer_stats` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_date` date NOT NULL COMMENT '访问日期',
  `referer_domain` varchar(200) NOT NULL COMMENT '来源域名',
  `referer_type` enum('direct','search','social','referral','email','other') DEFAULT 'other' COMMENT '来源类型',
  `visit_count` int(11) DEFAULT 0 COMMENT '访问次数',
  `unique_visitors` int(11) DEFAULT 0 COMMENT '独立访客数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_domain` (`visit_date`, `referer_domain`),
  KEY `idx_visit_date` (`visit_date`),
  KEY `idx_referer_type` (`referer_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='来源统计表';

-- 6. 系统配置表 - 存储系统配置信息
DROP TABLE IF EXISTS `visit_config`;
CREATE TABLE `visit_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT '' COMMENT '配置描述',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO `visit_config` (`config_key`, `config_value`, `description`) VALUES
('track_enabled', '1', '是否启用访问统计'),
('exclude_ips', '127.0.0.1,::1', '排除的IP地址列表'),
('exclude_bots', '1', '是否排除爬虫访问'),
('session_timeout', '1800', '会话超时时间(秒)'),
('data_retention_days', '365', '数据保留天数'),
('enable_geolocation', '0', '是否启用地理位置识别'),
('timezone', 'Asia/Shanghai', '时区设置');

SET FOREIGN_KEY_CHECKS = 1;
