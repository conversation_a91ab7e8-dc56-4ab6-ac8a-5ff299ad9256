<?php
/**
 * 示例页面 - 演示简单的访问统计功能
 */

// 引入简单访问统计功能
//require_once __DIR__ . '/simple_visit_counter.php';

// 记录当前页面访问
recordVisit($data_file, $log_file);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007cba;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007cba;
            margin: 0;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .code-block code {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-left: 4px solid #007cba;
            border-radius: 4px;
        }
        .feature-list li strong {
            color: #007cba;
        }
        .admin-link {
            display: inline-block;
            background: #007cba;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
        .admin-link:hover {
            background: #005a87;
        }
        .install-link {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
        .install-link:hover {
            background: #218838;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>简单访问统计系统</h1>
            <p>功能演示页面</p>
        </div>

        <div class="section">
            <h2>系统简介</h2>
            <p>这是一个简单的PHP访问统计系统，使用文件存储访问数据，无需数据库。本页面已经集成了访问统计功能，您的访问已被记录。</p>
        </div>

        <!-- 显示访问统计 -->
        <?php displayVisitStats($data_file); ?>

        <div class="section">
            <h2>主要功能</h2>
            <ul class="feature-list">
                <li><strong>访问量统计：</strong>记录每个页面的访问次数、独立访客数、页面浏览量</li>
                <li><strong>文件存储：</strong>使用JSON文件存储数据，无需数据库配置</li>
                <li><strong>数据去重：</strong>自动过滤爬虫访问，基于IP统计独立访客</li>
                <li><strong>实时显示：</strong>页面访问时实时更新统计数据</li>
                <li><strong>趋势分析：</strong>显示最近7天的访问趋势</li>
                <li><strong>热门页面：</strong>统计并显示访问量最高的页面</li>
            </ul>
        </div>

        <div class="section">
            <h2>使用方法</h2>

            <h3>1. 基本使用</h3>
            <p>在需要统计访问量的页面中添加以下代码：</p>
            <div class="code-block">
                <code>
&lt;?php<br>
// 引入访问统计功能<br>
require_once 'simple_visit_counter.php';<br>
<br>
// 记录访问<br>
recordVisit($data_file, $log_file);<br>
<br>
// 显示统计数据<br>
displayVisitStats($data_file);<br>
?&gt;
                </code>
            </div>

            <h3>2. 仅记录访问（不显示统计）</h3>
            <p>如果只想记录访问而不显示统计数据：</p>
            <div class="code-block">
                <code>
&lt;?php<br>
require_once 'simple_visit_counter.php';<br>
recordVisit($data_file, $log_file);<br>
?&gt;
                </code>
            </div>

            <h3>3. 查看统计数据</h3>
            <p>可以创建一个专门的统计页面：</p>
            <div class="code-block">
                <code>
&lt;?php<br>
require_once 'simple_visit_counter.php';<br>
$stats = getVisitStats($data_file);<br>
// 使用 $stats 数组显示自定义统计界面<br>
?&gt;
                </code>
            </div>
        </div>

        <div class="section">
            <h2>数据存储</h2>
            <p>系统使用文件存储数据，会创建以下文件：</p>
            <ul>
                <li><strong>visit_data.txt：</strong>访问统计数据（JSON格式）</li>
                <li><strong>visit_log.txt：</strong>访问详细日志</li>
            </ul>
            <p>数据文件会自动创建，无需手动配置。系统会自动清理30天前的数据。</p>
        </div>

        <div class="section">
            <h2>文件说明</h2>
            <p>系统包含的主要文件：</p>
            <ul>
                <li><strong>simple_visit_counter.php：</strong>核心统计功能</li>
                <li><strong>example.php：</strong>使用示例页面</li>
                <li><strong>visit_data.txt：</strong>统计数据文件（自动生成）</li>
                <li><strong>visit_log.txt：</strong>访问日志文件（自动生成）</li>
            </ul>
        </div>

        <div class="section">
            <h2>技术特性</h2>
            <ul class="feature-list">
                <li><strong>轻量级：</strong>单文件实现，代码简洁，性能优秀</li>
                <li><strong>无依赖：</strong>不需要数据库，仅使用PHP内置函数</li>
                <li><strong>兼容性：</strong>支持PHP 5.4+，兼容性极佳</li>
                <li><strong>自动清理：</strong>自动清理30天前的数据，避免文件过大</li>
                <li><strong>易部署：</strong>上传文件即可使用，无需复杂配置</li>
            </ul>
        </div>

        <div class="section">
            <h2>当前访问信息</h2>
            <p>您的本次访问信息：</p>
            <ul>
                <li><strong>页面URL：</strong><?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?></li>
                <li><strong>访问时间：</strong><?php echo date('Y-m-d H:i:s'); ?></li>
                <li><strong>IP地址：</strong><?php echo $_SERVER['REMOTE_ADDR'] ? $_SERVER['REMOTE_ADDR'] : '未知'; ?></li>
                <li><strong>用户代理：</strong><?php echo htmlspecialchars(substr($_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT']: '未知', 0, 80)); ?>...</li>
            </ul>
            <p style="color: #28a745; font-weight: bold;">✅ 您的访问已被成功记录！</p>
        </div>
    </div>
</body>
</html>
