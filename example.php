<?php
/**
 * 示例页面 - 演示如何使用PHPCMS访问统计功能
 */

// 设置页面标题（可选）
$page_title = '示例页面 - PHPCMS访问统计演示';

// 设置是否异步记录（可选，默认false）
$track_async = false;

// 引入访问统计中间件
require_once __DIR__ . '/track_visit.php';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007cba;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007cba;
            margin: 0;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .code-block code {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-left: 4px solid #007cba;
            border-radius: 4px;
        }
        .feature-list li strong {
            color: #007cba;
        }
        .admin-link {
            display: inline-block;
            background: #007cba;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
        .admin-link:hover {
            background: #005a87;
        }
        .install-link {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
        .install-link:hover {
            background: #218838;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PHPCMS访问统计系统</h1>
            <p>功能演示页面</p>
        </div>

        <div class="section">
            <h2>系统简介</h2>
            <p>这是一个为PHPCMS开发的访问统计系统，可以统计网站的每日访问量并存储到MySQL数据库中。本页面已经集成了访问统计功能，您的访问已被记录。</p>
        </div>

        <div class="section">
            <h2>主要功能</h2>
            <ul class="feature-list">
                <li><strong>访问量统计：</strong>记录每个页面的访问次数、独立访客数、页面浏览量等</li>
                <li><strong>访客分析：</strong>分析访客的浏览器、操作系统、设备类型等信息</li>
                <li><strong>数据去重：</strong>自动过滤爬虫访问和重复访问，确保数据准确性</li>
                <li><strong>实时统计：</strong>支持实时记录访问数据，也支持异步记录提高性能</li>
                <li><strong>数据导出：</strong>支持将统计数据导出为CSV格式</li>
                <li><strong>管理界面：</strong>提供友好的Web管理界面查看统计数据</li>
            </ul>
        </div>

        <div class="section">
            <h2>使用方法</h2>
            
            <h3>1. 安装系统</h3>
            <p>首先需要安装数据库表和配置系统：</p>
            <a href="install.php" class="install-link">点击这里安装系统</a>
            
            <h3>2. 在页面中集成统计功能</h3>
            <p>在需要统计访问量的页面顶部添加以下代码：</p>
            <div class="code-block">
                <code>
&lt;?php<br>
// 设置页面标题（可选）<br>
$page_title = '您的页面标题';<br>
<br>
// 引入访问统计中间件<br>
require_once 'track_visit.php';<br>
?&gt;
                </code>
            </div>

            <h3>3. 高并发场景的异步记录</h3>
            <p>对于高并发的网站，可以使用异步记录方式：</p>
            <div class="code-block">
                <code>
&lt;?php<br>
// 设置异步记录<br>
$track_async = true;<br>
$page_title = '您的页面标题';<br>
<br>
require_once 'track_visit.php';<br>
?&gt;
                </code>
            </div>

            <h3>4. 查看统计数据</h3>
            <p>通过管理界面查看详细的访问统计数据：</p>
            <a href="admin/visit_stats.php" class="admin-link">访问统计管理</a>
            
            <div class="note">
                <strong>注意：</strong>默认管理密码是 <code>admin123</code>，安装后请及时修改。
            </div>
        </div>

        <div class="section">
            <h2>数据库表结构</h2>
            <p>系统会创建以下数据库表：</p>
            <ul>
                <li><strong>visit_daily_stats：</strong>每日访问统计表</li>
                <li><strong>visit_logs：</strong>访问详细记录表</li>
                <li><strong>site_stats：</strong>网站总体统计表</li>
                <li><strong>visitors：</strong>访客信息表</li>
            </ul>
        </div>

        <div class="section">
            <h2>配置说明</h2>
            <p>系统的主要配置文件：</p>
            <ul>
                <li><strong>config/database.php：</strong>数据库连接配置</li>
                <li><strong>track_visit.php：</strong>访问统计中间件</li>
                <li><strong>classes/VisitTracker.php：</strong>核心统计类</li>
                <li><strong>admin/visit_stats.php：</strong>管理界面</li>
            </ul>
        </div>

        <div class="section">
            <h2>技术特性</h2>
            <ul class="feature-list">
                <li><strong>轻量级：</strong>代码简洁，性能优秀，不会影响网站正常运行</li>
                <li><strong>安全性：</strong>使用PDO预处理语句，防止SQL注入攻击</li>
                <li><strong>兼容性：</strong>支持PHP 7.0+，兼容主流的MySQL版本</li>
                <li><strong>可扩展：</strong>模块化设计，易于扩展和定制</li>
                <li><strong>易用性：</strong>一键安装，简单配置即可使用</li>
            </ul>
        </div>

        <div class="section">
            <h2>当前页面统计信息</h2>
            <p>本页面已经集成了访问统计功能，您的访问信息包括：</p>
            <ul>
                <li>页面URL：<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?></li>
                <li>访问时间：<?php echo date('Y-m-d H:i:s'); ?></li>
                <li>IP地址：<?php echo $_SERVER['REMOTE_ADDR'] ?? '未知'; ?></li>
                <li>用户代理：<?php echo htmlspecialchars(substr($_SERVER['HTTP_USER_AGENT'] ?? '未知', 0, 100)); ?>...</li>
            </ul>
        </div>
    </div>
</body>
</html>
