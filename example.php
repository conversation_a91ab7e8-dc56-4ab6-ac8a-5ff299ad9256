<?php
/**
 * 示例页面 - 演示如何在普通页面中集成访问统计
 */

// 设置页面标题
$page_title = '示例页面 - 访问统计集成演示';

// 引入访问统计 - 就这么简单！
require_once __DIR__ . '/track.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f5f7fa; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #3498db, #2ecc71); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.2em; margin-bottom: 10px; }
        .header p { font-size: 1.1em; opacity: 0.9; }
        
        .content { background: white; padding: 30px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .content h2 { color: #2c3e50; margin-bottom: 20px; }
        .content p { margin-bottom: 15px; }
        
        .code-example { background: #2c3e50; color: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; overflow-x: auto; }
        .code-example code { font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 14px; }
        
        .info-box { background: #e8f4fd; border-left: 4px solid #3498db; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0; }
        .success-box { background: #d4edda; border-left: 4px solid #2ecc71; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0; }
        
        .btn { display: inline-block; background: linear-gradient(135deg, #3498db, #2ecc71); color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s; margin: 5px; }
        .btn:hover { transform: translateY(-2px); }
        
        .stats-mini { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
        .stats-mini h3 { color: #2c3e50; margin-bottom: 15px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; }
        .stat-item { background: white; padding: 15px; border-radius: 6px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stat-number { font-size: 1.5em; font-weight: bold; color: #3498db; }
        .stat-label { font-size: 0.9em; color: #7f8c8d; }
        
        .navigation { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .navigation h3 { color: #2c3e50; margin-bottom: 15px; }
        
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .header { padding: 20px; }
            .header h1 { font-size: 1.8em; }
            .content { padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>📄 示例页面</h1>
            <p>演示如何在普通页面中集成访问统计功能</p>
        </div>

        <!-- 集成说明 -->
        <div class="content">
            <h2>🔧 集成方法</h2>
            <p>在这个页面中，我们只需要添加两行代码就完成了访问统计的集成：</p>
            
            <div class="code-example">
                <code>
&lt;?php<br>
// 设置页面标题（可选）<br>
$page_title = '示例页面 - 访问统计集成演示';<br>
<br>
// 引入访问统计 - 就这么简单！<br>
require_once __DIR__ . '/track.php';<br>
?&gt;
                </code>
            </div>
            
            <div class="success-box">
                <strong>✅ 集成成功！</strong> 您的访问已经被自动记录到数据库中，包括：
                <ul style="margin-top: 10px; margin-left: 20px;">
                    <li>访问时间：<?php echo date('Y-m-d H:i:s'); ?></li>
                    <li>页面URL：<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?></li>
                    <li>访客IP：<?php echo $_SERVER['REMOTE_ADDR'] ?? '未知'; ?></li>
                    <li>浏览器信息：<?php echo htmlspecialchars(substr($_SERVER['HTTP_USER_AGENT'] ?? '未知', 0, 50)); ?>...</li>
                </ul>
            </div>
        </div>

        <!-- 功能特性 -->
        <div class="content">
            <h2>✨ 自动记录的数据</h2>
            <p>系统会自动记录以下访问数据：</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px;">👥 访客信息</h4>
                    <ul style="color: #7f8c8d; font-size: 0.9em;">
                        <li>唯一访客标识</li>
                        <li>IP地址</li>
                        <li>访问时间</li>
                        <li>会话信息</li>
                    </ul>
                </div>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px;">📱 设备信息</h4>
                    <ul style="color: #7f8c8d; font-size: 0.9em;">
                        <li>浏览器类型</li>
                        <li>操作系统</li>
                        <li>设备类型</li>
                        <li>屏幕分辨率</li>
                    </ul>
                </div>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px;">📄 页面信息</h4>
                    <ul style="color: #7f8c8d; font-size: 0.9em;">
                        <li>页面URL</li>
                        <li>页面标题</li>
                        <li>来源页面</li>
                        <li>停留时间</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 模拟统计数据 -->
        <div class="stats-mini">
            <h3>📊 实时统计预览</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="pageViews">-</div>
                    <div class="stat-label">页面浏览</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="visitors">-</div>
                    <div class="stat-label">访客数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="sessions">-</div>
                    <div class="stat-label">会话数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="avgTime">-</div>
                    <div class="stat-label">平均时长</div>
                </div>
            </div>
        </div>

        <!-- 更多示例 -->
        <div class="content">
            <h2>🎯 更多集成示例</h2>
            
            <h3>在不同类型页面中使用：</h3>
            
            <div class="code-example">
                <code>
// 博客文章页面<br>
$page_title = '如何使用PHP开发网站 - 我的博客';<br>
require_once 'track.php';<br>
<br>
// 产品页面<br>
$page_title = '产品名称 - 在线商店';<br>
require_once 'track.php';<br>
<br>
// 新闻页面<br>
$page_title = '最新新闻标题 - 新闻网站';<br>
require_once 'track.php';
                </code>
            </div>
            
            <div class="info-box">
                <strong>💡 提示：</strong>
                页面标题是可选的，如果不设置，系统会自动从URL中推断页面名称。
                建议为每个页面设置有意义的标题，这样在统计报表中更容易识别。
            </div>
        </div>

        <!-- 导航链接 -->
        <div class="navigation">
            <h3>🔗 相关页面</h3>
            <a href="dashboard.php" class="btn">📊 查看统计仪表板</a>
            <a href="demo.php" class="btn">📖 查看演示页面</a>
            <a href="install.php" class="btn">⚙️ 系统安装</a>
        </div>

        <!-- 页面底部 -->
        <div style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <p>这个页面的访问数据已经被记录，您可以在统计仪表板中查看详细信息。</p>
        </div>
    </div>

    <script>
        // 模拟实时数据更新
        function updateStats() {
            document.getElementById('pageViews').textContent = Math.floor(Math.random() * 500) + 100;
            document.getElementById('visitors').textContent = Math.floor(Math.random() * 200) + 50;
            document.getElementById('sessions').textContent = Math.floor(Math.random() * 150) + 30;
            document.getElementById('avgTime').textContent = (Math.random() * 5 + 1).toFixed(1) + 'm';
        }
        
        // 页面加载时更新数据
        updateStats();
        
        // 每30秒更新一次
        setInterval(updateStats, 30000);
    </script>
</body>
</html>
