<?php
/**
 * 访问量记录中间件
 * 在需要统计访问量的页面中引入此文件
 * 
 * 使用方法：
 * 在页面顶部添加：require_once 'track_visit.php';
 */

// 防止直接访问
if (!defined('TRACK_VISIT_INCLUDED')) {
    define('TRACK_VISIT_INCLUDED', true);
}

// 引入访问统计类
require_once __DIR__ . '/classes/VisitTracker.php';

try {
    // 创建访问统计实例
    $visitTracker = new VisitTracker();
    
    // 获取当前页面URL
    $currentUrl = getCurrentPageUrl();
    
    // 获取来源页面
    $referer = $_SERVER['HTTP_REFERER'] ?? null;
    
    // 记录访问
    $visitTracker->recordVisit($currentUrl, $referer);
    
} catch (Exception $e) {
    // 静默处理错误，不影响页面正常显示
    error_log("访问统计错误: " . $e->getMessage());
}

/**
 * 获取当前页面完整URL
 * @return string
 */
function getCurrentPageUrl() {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $uri = $_SERVER['REQUEST_URI'] ?? '/';
    
    return $protocol . $host . $uri;
}

/**
 * 异步记录访问（可选，用于高并发场景）
 * @param string $pageUrl
 * @param string $referer
 */
function trackVisitAsync($pageUrl, $referer = null) {
    // 将访问信息写入队列或日志文件，由后台脚本处理
    $logData = [
        'url' => $pageUrl,
        'referer' => $referer,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'timestamp' => time()
    ];
    
    $logFile = __DIR__ . '/logs/visit_queue.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
}
?>
