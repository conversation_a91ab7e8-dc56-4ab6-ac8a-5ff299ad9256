<?php
/**
 * 用户代理解析类
 * 解析浏览器、操作系统、设备类型等信息
 */

class UserAgent {
    
    /**
     * 解析用户代理字符串
     */
    public function parse($userAgent) {
        $result = [
            'browser' => 'Unknown',
            'browser_version' => '',
            'os' => 'Unknown',
            'device_type' => 'desktop'
        ];
        
        if (empty($userAgent)) {
            return $result;
        }
        
        // 解析浏览器
        $browserInfo = $this->parseBrowser($userAgent);
        $result['browser'] = $browserInfo['name'];
        $result['browser_version'] = $browserInfo['version'];
        
        // 解析操作系统
        $result['os'] = $this->parseOS($userAgent);
        
        // 解析设备类型
        $result['device_type'] = $this->parseDeviceType($userAgent);
        
        return $result;
    }
    
    /**
     * 解析浏览器
     */
    private function parseBrowser($userAgent) {
        $browsers = [
            'Edge' => '/Edge\/([0-9\.]+)/i',
            'Chrome' => '/Chrome\/([0-9\.]+)/i',
            'Firefox' => '/Firefox\/([0-9\.]+)/i',
            'Safari' => '/Version\/([0-9\.]+).*Safari/i',
            'Opera' => '/(?:Opera|OPR)\/([0-9\.]+)/i',
            'Internet Explorer' => '/MSIE ([0-9\.]+)/i',
            'IE11' => '/Trident.*rv:([0-9\.]+)/i'
        ];
        
        foreach ($browsers as $browser => $pattern) {
            if (preg_match($pattern, $userAgent, $matches)) {
                $version = isset($matches[1]) ? $matches[1] : '';
                return [
                    'name' => $browser === 'IE11' ? 'Internet Explorer' : $browser,
                    'version' => $version
                ];
            }
        }
        
        return ['name' => 'Unknown', 'version' => ''];
    }
    
    /**
     * 解析操作系统
     */
    private function parseOS($userAgent) {
        $os_array = [
            'Windows 11' => '/Windows NT 10\.0.*Build 22000/i',
            'Windows 10' => '/Windows NT 10\.0/i',
            'Windows 8.1' => '/Windows NT 6\.3/i',
            'Windows 8' => '/Windows NT 6\.2/i',
            'Windows 7' => '/Windows NT 6\.1/i',
            'Windows Vista' => '/Windows NT 6\.0/i',
            'Windows XP' => '/Windows NT 5\.1/i',
            'macOS Big Sur' => '/Mac OS X 10[._]16/i',
            'macOS Catalina' => '/Mac OS X 10[._]15/i',
            'macOS Mojave' => '/Mac OS X 10[._]14/i',
            'macOS High Sierra' => '/Mac OS X 10[._]13/i',
            'macOS Sierra' => '/Mac OS X 10[._]12/i',
            'Mac OS X' => '/Mac OS X ([0-9_\.]+)/i',
            'macOS' => '/Mac OS X|macOS/i',
            'iOS' => '/iPhone OS ([0-9_\.]+)|iOS ([0-9_\.]+)/i',
            'Android' => '/Android ([0-9\.]+)/i',
            'Ubuntu' => '/Ubuntu/i',
            'Linux' => '/Linux/i',
            'Chrome OS' => '/CrOS/i'
        ];
        
        foreach ($os_array as $os => $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return $os;
            }
        }
        
        return 'Unknown';
    }
    
    /**
     * 解析设备类型
     */
    private function parseDeviceType($userAgent) {
        // 移动设备检测
        $mobilePatterns = [
            '/Mobile/i',
            '/Android.*Mobile/i',
            '/iPhone/i',
            '/iPod/i',
            '/BlackBerry/i',
            '/Windows Phone/i',
            '/Opera Mini/i',
            '/IEMobile/i'
        ];
        
        foreach ($mobilePatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return 'mobile';
            }
        }
        
        // 平板设备检测
        $tabletPatterns = [
            '/iPad/i',
            '/Android(?!.*Mobile)/i',
            '/Tablet/i',
            '/Kindle/i',
            '/PlayBook/i',
            '/Nexus 7|Nexus 9|Nexus 10/i'
        ];
        
        foreach ($tabletPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return 'tablet';
            }
        }
        
        return 'desktop';
    }
    
    /**
     * 检查是否为移动设备
     */
    public function isMobile($userAgent = null) {
        if ($userAgent === null) {
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '';
        }
        
        $deviceType = $this->parseDeviceType($userAgent);
        return in_array($deviceType, ['mobile', 'tablet']);
    }
    
    /**
     * 检查是否为爬虫
     */
    public function isBot($userAgent = null) {
        if ($userAgent === null) {
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '';
        }
        
        $botPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i',
            '/curl/i',
            '/wget/i',
            '/Googlebot/i',
            '/Bingbot/i',
            '/Slurp/i',
            '/DuckDuckBot/i',
            '/Baiduspider/i',
            '/YandexBot/i',
            '/facebookexternalhit/i',
            '/Twitterbot/i',
            '/LinkedInBot/i',
            '/WhatsApp/i',
            '/Applebot/i'
        ];
        
        foreach ($botPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取浏览器图标
     */
    public function getBrowserIcon($browser) {
        $icons = [
            'Chrome' => '🌐',
            'Firefox' => '🦊',
            'Safari' => '🧭',
            'Edge' => '🔷',
            'Opera' => '🎭',
            'Internet Explorer' => '🌍'
        ];
        
        return $icons[$browser] ? $icons[$browser] : '🌐';
    }
    
    /**
     * 获取操作系统图标
     */
    public function getOSIcon($os) {
        $icons = [
            'Windows' => '🪟',
            'macOS' => '🍎',
            'iOS' => '📱',
            'Android' => '🤖',
            'Linux' => '🐧',
            'Ubuntu' => '🟠'
        ];
        
        foreach ($icons as $osName => $icon) {
            if (strpos($os, $osName) !== false) {
                return $icon;
            }
        }
        
        return '💻';
    }
    
    /**
     * 获取设备类型图标
     */
    public function getDeviceIcon($deviceType) {
        $icons = [
            'desktop' => '💻',
            'mobile' => '📱',
            'tablet' => '📱'
        ];
        
        return $icons[$deviceType] ? $icons[$deviceType] : '💻';
    }
}
